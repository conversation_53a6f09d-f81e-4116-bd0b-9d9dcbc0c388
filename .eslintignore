# The build artifacts of the lib-jiti-meet project.
lib-jitsi-meet.*
dist/
types/

# Third-party source code which we (1) do not want to modify or (2) try to
# modify as little as possible.
doc/example/libs/*

# ESLint will by default ignore its own configuration file. However, there does
# not seem to be a reason why we will want to risk being inconsistent with our
# remaining JavaScript source code.
!.eslintrc.js
