---
import Icon from './Icon.astro';
import type { HTMLAttributes } from 'astro/types';

interface Props extends Omit<HTMLAttributes<'a'>, 'title'> {
	title: string;
	description?: string;
}

const { title, description, ...attributes } = Astro.props;
---

<div class="sl-link-card">
	<span class="sl-flex stack">
		<a {...attributes}>
			<span class="title" set:html={title} />
		</a>
		{description && <span class="description" set:html={description} />}
	</span>
	<Icon name="right-arrow" size="1.333em" class="icon rtl:flip" />
</div>

<style>
	.sl-link-card {
		display: grid;
		grid-template-columns: 1fr auto;
		gap: 0.5rem;
		border: 1px solid var(--sl-color-gray-5);
		border-radius: 0.5rem;
		padding: 1rem;
		box-shadow: var(--sl-shadow-sm);
		position: relative;
	}

	a {
		text-decoration: none;
		line-height: var(--sl-line-height-headings);
	}

	/* a11y fix for https://github.com/withastro/starlight/issues/487 */
	a::before {
		content: '';
		position: absolute;
		inset: 0;
	}

	.stack {
		flex-direction: column;
		gap: 0.5rem;
	}

	.title {
		color: var(--sl-color-white);
		font-weight: 600;
		font-size: var(--sl-text-lg);
	}

	.description {
		color: var(--sl-color-gray-3);
		line-height: 1.5;
	}

	.icon {
		color: var(--sl-color-gray-3);
	}

	/* Hover state */
	.sl-link-card:hover {
		background: var(--sl-color-gray-7, var(--sl-color-gray-6));
		border-color: var(--sl-color-gray-2);
	}

	.sl-link-card:hover .icon {
		color: var(--sl-color-white);
	}
</style>
