---
import { slugToLocaleData } from '../utils/slugs';
import { useTranslations } from '../utils/translations';
import { processFileTree } from './rehype-file-tree';

const slug = Astro.url.pathname.replace(/^\//, '').replace(/\/$/, '');
const t = useTranslations(slugToLocaleData(slug).locale);

const fileTreeHtml = await Astro.slots.render('default');
const html = processFileTree(fileTreeHtml, t('fileTree.directory'));
---

<starlight-file-tree set:html={html} class="not-content" data-pagefind-ignore />

<style>
	starlight-file-tree {
		--x-space: 1.5rem;
		--y-space: 0.125rem;
		--y-pad: 0;

		display: block;
		border: 1px solid var(--sl-color-gray-5);
		padding: 1rem;
		background-color: var(--sl-color-gray-6);
		font-size: var(--sl-text-xs);
		font-family: var(--__sl-font-mono);
		overflow-x: auto;
	}

	starlight-file-tree :global(.directory > details) {
		border: 0;
		padding: 0;
		padding-inline-start: var(--x-space);
		background: transparent;
	}

	starlight-file-tree :global(.directory > details > summary) {
		margin-inline-start: calc(-1 * var(--x-space));
		border: 0;
		padding: var(--y-pad) 0.625rem;
		font-weight: normal;
		color: var(--sl-color-white);
		max-width: 100%;
	}

	starlight-file-tree :global(.directory > details > summary::marker),
	starlight-file-tree :global(.directory > details > summary::-webkit-details-marker) {
		color: var(--sl-color-gray-3);
	}

	starlight-file-tree :global(.directory > details > summary:hover),
	starlight-file-tree :global(.directory > details > summary:hover .tree-icon) {
		cursor: pointer;
		color: var(--sl-color-text-accent);
		fill: var(--sl-color-text-accent);
	}

	starlight-file-tree :global(.directory > details > summary:hover ~ ul) {
		border-color: var(--sl-color-gray-4);
	}

	starlight-file-tree :global(.directory > details > summary:hover .highlight .tree-icon) {
		fill: var(--sl-color-text-invert);
	}

	starlight-file-tree :global(ul) {
		margin-inline-start: 0.5rem;
		border-inline-start: 1px solid var(--sl-color-gray-5);
		padding: 0;
		padding-inline-start: 0.125rem;
		list-style: none;
	}

	starlight-file-tree > :global(ul) {
		margin: 0;
		border: 0;
		padding: 0;
	}

	starlight-file-tree :global(li) {
		margin: var(--y-space) 0;
		padding: var(--y-pad) 0;
	}

	starlight-file-tree :global(.file) {
		margin-inline-start: calc(var(--x-space) - 0.125rem);
		color: var(--sl-color-white);
	}

	starlight-file-tree :global(.tree-entry) {
		display: inline-flex;
		align-items: flex-start;
		flex-wrap: wrap;
		max-width: calc(100% - 1rem);
	}

	@media (min-width: 30em) {
		starlight-file-tree :global(.tree-entry) {
			flex-wrap: nowrap;
		}
	}

	starlight-file-tree :global(.tree-entry > :first-child) {
		flex-shrink: 0;
	}

	starlight-file-tree :global(.empty) {
		color: var(--sl-color-gray-3);
		padding-inline-start: 0.375rem;
	}

	starlight-file-tree :global(.comment) {
		color: var(--sl-color-gray-3);
		padding-inline-start: 1.625rem;
		max-width: 24rem;
		min-width: 12rem;
	}

	starlight-file-tree :global(.highlight) {
		display: inline-block;
		border-radius: 0.25rem;
		padding-inline-end: 0.5rem;
		color: var(--sl-color-text-invert);
		background-color: var(--sl-color-text-accent);
	}

	starlight-file-tree :global(svg) {
		display: inline;
		fill: var(--sl-color-gray-3);
		vertical-align: middle;
		margin-inline: 0.25rem 0.375rem;
		width: 0.875rem;
		height: 0.875rem;
	}

	starlight-file-tree :global(.highlight svg.tree-icon) {
		fill: var(--sl-color-text-invert);
	}
</style>
