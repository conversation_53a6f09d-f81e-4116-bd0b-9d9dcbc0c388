package viclass.editor.geo.impl.controller

import CmdProto.CmdMeta
import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.logging.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.koin.core.annotation.Singleton
import viclass.editor.geo.dbentity.AngleType
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.DocRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.renderdata.GeoStrokeStyle
import viclass.editor.geo.dbentity.renderdata.RenderVertex
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.exceptions.ViException
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.models.request.*
import viclass.editor.geo.models.response.FetchDocResponse
import viclass.proto.feature_common.FeatureCommonCmd
import viclass.proto.geometry.cmd.GeoCmd
import viclass.proto.geometry.msg.GeoMsg
import kotlin.math.abs

@Singleton(binds = [DocumentController::class])
class DocumentController constructor(val commander: GeoDocCommander) : Logging {

    suspend fun create(call: ApplicationCall) {
        val req = call.receive<CreateGeoDocRequest>()
        val doc = commander.newDoc(req.numDimension, req.canvasWidth, req.canvasHeight, req.unit)

        call.respond(
            FetchDocResponse(
                doc.id,
                doc.numDim,
                doc.renderDoc.state.toDocRenderProp(),
                doc.docDefaultElRenderProp,
                doc.renderDoc.elements,
            )
        )
    }

    suspend fun fetch(call: ApplicationCall) {
        val docId = call.parameters["globalId"] ?: return call.respond(
            HttpStatusCode.BadRequest,
            mapOf("message" to "Missing or malformed globalId"),
        )
        val doc = commander.retrieveDoc(docId) ?: run {
            return call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
        }

        call.respond(
            HttpStatusCode.OK, FetchDocResponse(
                doc.id,
                doc.numDim,
                doc.renderDoc.state.toDocRenderProp(),
                doc.docDefaultElRenderProp,
                doc.renderDoc.elements,
            )
        )
    }

    suspend fun processCmd(call: ApplicationCall) {
        val data = call.receive<ByteArray>()

        val metaLength = data[0].toInt()
        val cmdMetaArrBuf = data.copyOfRange(1, metaLength + 1)
        val cmdDataArrBuf = data.copyOfRange(metaLength + 1, data.size)

        val cmdMeta = try {
            CmdMeta.CmdMetaProto.parseFrom(cmdMetaArrBuf)
        } catch (t: Throwable) {
            logger.error("failed to parse CmdMeta: ", t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("message" to "failed to parse CmdMeta: ${t.message}")
            )
        }

        if (cmdMeta.channelCode != 2) {
            return call.respond(HttpStatusCode.BadRequest, mapOf("message" to "invalid channel code"))
        }

        val globalId: String = call.parameters["globalId"]
            ?: return call.respond(HttpStatusCode.BadRequest, mapOf("message" to "missing global id"))

        when (cmdMeta.cmdType) {
            FeatureCommonCmd.FCCmdTypeProto.INSERT_DOC_VALUE -> {
                processInsertDocCmd(call, cmdDataArrBuf)
            }

            FeatureCommonCmd.FCCmdTypeProto.INSERT_LAYER_VALUE -> {
                // do nothing
                call.respond(HttpStatusCode.OK)
            }

            FeatureCommonCmd.FCCmdTypeProto.REMOVE_DOC_VALUE -> {
                // do nothing
                call.respond(HttpStatusCode.OK)
            }

            GeoCmd.CmdTypeProto.UPDATE_ELS_PROP_VALUE -> {
                updateElsPropProto(call, globalId, cmdDataArrBuf)
            }

            GeoCmd.CmdTypeProto.UPDATE_DOC_STATE_VALUE -> {
                updateDocStateProto(call, globalId, cmdMeta, cmdDataArrBuf)
            }

            GeoCmd.CmdTypeProto.UPDATE_DEFAULT_EL_RENDER_PROPS_VALUE -> {
                updateDocDefaultElRenderProps(call, globalId, cmdMeta, cmdDataArrBuf)
            }

            else -> {
                logger.error("Not accept cmd type {} from cmd meta {}", cmdMeta.cmdType, cmdMeta)
                return call.respond(
                    HttpStatusCode.BadRequest,
                    mapOf("message" to "not accept cmd type ${cmdMeta.cmdType}")
                )
            }
        }
    }

    private suspend fun processInsertDocCmd(
        call: ApplicationCall,
        cmdDataArrBuf: ByteArray
    ) {
        val insertCmd = try {
            GeoCmd.InsertDocCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            return call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("message" to "failed to parse InsertDocCmdProto: ${t.message}")
            )
        }

        if (insertCmd.globalId != "") {
            return call.respond(
                HttpStatusCode.OK,
                mapOf("globalId" to insertCmd.globalId, "message" to "successful")
            )
        }

        val width = abs(insertCmd.boundary.start.x - insertCmd.boundary.end.x)
        val height = abs(insertCmd.boundary.start.x - insertCmd.boundary.end.x)

        val numDim = when (insertCmd.kind) {
            GeoMsg.GeoKind.Geo2D -> 2
            GeoMsg.GeoKind.Geo3D -> 3
            else -> {
                call.respond(
                    HttpStatusCode.BadRequest
                )
                return
            }
        }

        val doc = commander.newDoc(numDim, width, height, insertCmd.unit)

        return call.respond(HttpStatusCode.OK, mapOf("globalId" to doc.id, "message" to "successful"))
    }

    private suspend fun updateElsPropProto(
        call: ApplicationCall,
        globalId: String,
        cmdDataArrBuf: ByteArray
    ) {
        val cmd = try {
            GeoCmd.UpdateElsPropCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            return call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("message" to "failed to parse UpdateElsPropCmdProto: ${t.message}")
            )
        }

        val doc = commander.retrieveDoc(globalId) ?: run {
            return call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
        }

        val elementRenderProps = RenderProp()
        val cmderp = cmd.elRenderProps
        elementRenderProps.color = if (cmderp.hasColor()) cmderp.color else null
        elementRenderProps.lineColor = if (cmderp.hasLineColor()) cmderp.lineColor else null
        elementRenderProps.pointColor = if (cmderp.hasPointColor()) cmderp.pointColor else null
        elementRenderProps.strokeStyle =
            if (cmderp.hasStrokeStyle()) GeoStrokeStyle.valueOf(cmderp.strokeStyle) else null
        elementRenderProps.lineWeight = if (cmderp.hasLineWeight()) cmderp.lineWeight.toByte() else null
        elementRenderProps.opacity = if (cmderp.hasOpacity()) cmderp.opacity.toByte() else null
        elementRenderProps.hidden = if (cmderp.hasHidden()) cmderp.hidden else null
        elementRenderProps.showLabel = if (cmderp.hasShowLabel()) cmderp.showLabel else null
        elementRenderProps.label = if (cmderp.hasLabel()) cmderp.label else null
        elementRenderProps.swapLabelPosition = if (cmderp.hasSwapLabelPosition()) cmderp.swapLabelPosition else null
        elementRenderProps.labelType = if (cmderp.hasLabelType()) cmderp.labelType else null
        elementRenderProps.spaceFromArcToCorner =
            if (cmderp.hasSpaceFromArcToCorner()) cmderp.spaceFromArcToCorner else null
        elementRenderProps.showAngleTypes =
            if (cmderp.hasShowAngleTypes()) AngleType.valueOf(cmderp.showAngleTypes) else null
        elementRenderProps.angleArc = if (cmderp.hasAngleArc()) cmderp.angleArc else null
        elementRenderProps.enableEqualSegmentSign =
            if (cmderp.hasEnableEqualSegmentSign()) cmderp.enableEqualSegmentSign else null
        elementRenderProps.equalSegmentSign = if (cmderp.hasEqualSegmentSign()) cmderp.equalSegmentSign else null
        elementRenderProps.size = if (cmderp.hasSize()) cmderp.size else null
        elementRenderProps.showArcLabel = if (cmderp.hasShowArcLabel()) cmderp.showArcLabel else null
        elementRenderProps.arcLabelType = if (cmderp.hasArcLabelType()) cmderp.arcLabelType else null
        elementRenderProps.arcLabelContent = if (cmderp.hasArcLabelContent()) cmderp.arcLabelContent else null
        elementRenderProps.pointLabelType = if (cmderp.hasPointLabelType()) cmderp.pointLabelType else null
        elementRenderProps.pointLabelFreeContent = if (cmderp.hasPointLabelFreeContent()) cmderp.pointLabelFreeContent else null
        elementRenderProps.showPointLabel = if (cmderp.hasShowPointLabel()) cmderp.showPointLabel else null
        elementRenderProps.isShowAngleSize = if (cmderp.hasIsShowAngleSize()) cmderp.isShowAngleSize else null

        commander.updateElsProp(
            globalId,
            cmd.relIndexList,
            elementRenderProps
        )

        commander.replaceDocToDB(doc)

        call.respond(HttpStatusCode.OK)
    }

    private suspend fun updateDocStateProto(
        call: ApplicationCall, globalId: String,
        cmdMeta: CmdMeta.CmdMetaProto,
        cmdDataArrBuf: ByteArray
    ) {
        val cmd = try {
            GeoCmd.UpdateDocStateCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("message" to "failed to parse UpdateDocStateCmdProto: ${t.message}")
            )
            return
        }

        val doc = commander.retrieveDoc(globalId) ?: run {
            call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
            return
        }

        logger.debug("Request updateDocState string {}", call.request.toLogString())

        val docRenderProp = doc.renderDoc.state.toDocRenderProp()
        updateNewValueFromProtoToDocRenderProp(docRenderProp, cmd)

        val prop =
            commander.updateDocState(globalId, docRenderProp) ?: run {
                logger.error("doc {} failed to update doc state", globalId)
                return call.respond(HttpStatusCode.InternalServerError, "Document doesn't exist")
            }

        logger.info("doc {} updated prop {}", cmd.globalId, prop)

        call.respond(HttpStatusCode.OK, prop)
    }

    private suspend fun updateDocDefaultElRenderProps(
        call: ApplicationCall, globalId: String,
        cmdMeta: CmdMeta.CmdMetaProto,
        cmdDataArrBuf: ByteArray
    ) {
        val cmd = try {
            GeoCmd.UpdateDocDefaultElRenderPropsCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf("message" to "failed to parse UpdateDocDefaultElRenderPropsCmdProto: ${t.message}")
            )
            return
        }

        val doc = commander.retrieveDoc(globalId) ?: run {
            call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
            return
        }

        logger.debug("Request updateDocDefaultElRenderProps string {}", call.request.toLogString())

        val docDefaultElRenderProp = doc.docDefaultElRenderProp
        patchNewValueFromProtoToDefaultElRenderProps(docDefaultElRenderProp, cmd)

        val prop =
            commander.updateDocDefaultElRenderProps(globalId, docDefaultElRenderProp) ?: run {
                logger.error("doc {} failed to update doc default el render props", globalId)
                return call.respond(HttpStatusCode.InternalServerError, "Document doesn't exist")
            }

        logger.info("doc {} updated doc default el render props {}", cmd.globalId, prop)

        call.respond(HttpStatusCode.OK, prop)
    }

    private fun patchNewValueFromProtoToDefaultElRenderProps(
        docDefaultElRenderProp: DocDefaultElRenderProp,
        cmd: GeoCmd.UpdateDocDefaultElRenderPropsCmdProto
    ) {
        val cmdderp = cmd.docDefaultElRenderProps

        if (cmdderp.hasColor()) docDefaultElRenderProp.color = cmdderp.color
        if (cmdderp.hasLineColor()) docDefaultElRenderProp.lineColor = cmdderp.lineColor
        if (cmdderp.hasPointColor()) docDefaultElRenderProp.pointColor = cmdderp.pointColor
        if (cmdderp.hasStrokeStyle()) docDefaultElRenderProp.strokeStyle = GeoStrokeStyle.valueOf(cmdderp.strokeStyle)
        if (cmdderp.hasLineWeight()) docDefaultElRenderProp.lineWeight = cmdderp.lineWeight.toByte()
        if (cmdderp.hasOpacity()) docDefaultElRenderProp.opacity = cmdderp.opacity.toByte()
        if (cmdderp.hasSpaceFromArcToCorner()) docDefaultElRenderProp.spaceFromArcToCorner =
            cmdderp.spaceFromArcToCorner
    }

    private fun updateNewValueFromProtoToDocRenderProp(
        docRenderProp: DocRenderProp,
        cmd: GeoCmd.UpdateDocStateCmdProto
    ) {
        if (cmd.docRenderProp.hasScreenUnit()) docRenderProp.screenUnit = cmd.docRenderProp.screenUnit
        if (cmd.docRenderProp.hasCanvasWidth()) docRenderProp.canvasWidth = cmd.docRenderProp.canvasWidth
        if (cmd.docRenderProp.hasCanvasHeight()) docRenderProp.canvasHeight = cmd.docRenderProp.canvasHeight
        if (cmd.docRenderProp.hasScale()) docRenderProp.scale = cmd.docRenderProp.scale
        if (cmd.docRenderProp.translationList
                .isNotEmpty()
        ) docRenderProp.translation = cmd.docRenderProp.translationList.toDoubleArray()
        if (cmd.docRenderProp.rotationList
                .isNotEmpty()
        ) docRenderProp.rotation = cmd.docRenderProp.rotationList.toDoubleArray()
        if (cmd.docRenderProp.hasAxis()) docRenderProp.axis = cmd.docRenderProp.axis
        if (cmd.docRenderProp.hasGrid()) docRenderProp.grid = cmd.docRenderProp.grid
        if (cmd.docRenderProp.hasDetailGrid()) docRenderProp.detailGrid = cmd.docRenderProp.detailGrid
        if (cmd.docRenderProp.hasSnapMode()) docRenderProp.snapMode = cmd.docRenderProp.snapMode
        if (cmd.docRenderProp.hasSnapToExistingPoints()) docRenderProp.snapToExistingPoints =
            cmd.docRenderProp.snapToExistingPoints
        if (cmd.docRenderProp.hasSnapToGrid()) docRenderProp.snapToGrid = cmd.docRenderProp.snapToGrid
        if (cmd.docRenderProp.hasNamingMode()) docRenderProp.namingMode = cmd.docRenderProp.namingMode
    }

    suspend fun duplicateDocuments(call: ApplicationCall) {
        val docIds = call.receive<List<String>>()
        val result = commander.duplicateDocument(docIds)
        call.respond(HttpStatusCode.OK, mapOf("mapping" to result))
    }

    suspend fun deleteDocument(call: ApplicationCall) {
        logger.debug("Request deleteDocument to string {}", call.request.toLogString())
        val docId = call.parameters["docId"] ?: run {
            return call.respond(HttpStatusCode.BadRequest, "Must specify document id")
        }
        commander.retrieveDoc(docId) ?: run {
            return call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
        }
        commander.deleteDocument(docId)
        call.respond(HttpStatusCode.OK)
    }

    suspend fun deleteDocuments(call: ApplicationCall) {
        val docIds = call.receive<List<String>>()
        docIds.forEach { commander.deleteDocument(it) }
        call.respond(HttpStatusCode.OK)
    }

    suspend fun deleteRenderElements(call: ApplicationCall) {
        val docId = call.parameters["docId"] ?: run {
            return call.respond(HttpStatusCode.BadRequest, "Must specify document id")
        }
        val req = call.receive<DeleteRenderElementsRequest>()
        val doc = commander.retrieveDoc(docId) ?: run {
            return call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
        }
        val entity = commander.convertDocToEntity(doc)
        try {
            val (elIdxes, relIdxes) = commander.deleteRenderElements(docId, req.relIdxes)
            commander.replaceDocToDB(doc)
            return call.respond(HttpStatusCode.OK, mapOf("elIdxes" to elIdxes, "relIdxes" to relIdxes))
        } catch (t: ViException) {
            logger.error("error when update element state... ", t)
            commander.replaceDoc(entity)
            return call.respond(HttpStatusCode.BadRequest, t.message ?: "unknown error")
        } catch (t: Throwable) {
            logger.error("unknown error occurred... ", t)
            commander.replaceDoc(entity)
            return call.respond(HttpStatusCode.InternalServerError, "unknown error")
        }
    }

    suspend fun renamePointElement(call: ApplicationCall) {
        logger.debug("Request rename point element string {}", call.request.toLogString())
        val req: RenameElRequest = call.receive<RenameElRequest>()
        val docId = call.parameters["docId"] ?: return call.respond(HttpStatusCode.BadRequest, "Must specify document id")
        val doc = commander.retrieveDoc(docId) ?: return call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
        val entity = commander.convertDocToEntity(doc)
        try {
            val oldRenders = doc.renderDoc.elements.filter { it is RenderVertex && it.name == req.name && !it.valid}.map {
                commander.renamePointElement(docId, it.relIndex, generatePointName(doc, req.name))
            }.flatten()
            val newRenders = commander.renamePointElement(docId, req.relIndex, req.name)
            oldRenders + newRenders
            commander.replaceDocToDB(doc)
            return call.respond(HttpStatusCode.OK, oldRenders + newRenders)
        } catch (t: ViException) {
            logger.error("error when rename element... ", t)
            commander.replaceDoc(entity)
            return call.respond(HttpStatusCode.BadRequest, t.message ?: "invalid name")
        } catch (t: Throwable) {
            logger.error("unknown error occurred... ", t)
            commander.replaceDoc(entity)
            return call.respond(HttpStatusCode.InternalServerError, "unknown error")
        }
    }

    suspend fun updateElementsState(call: ApplicationCall) {
        val docId = call.parameters["docId"] ?: run {
            return call.respond(HttpStatusCode.BadRequest, "Must specify document id")
        }
        val req = call.receive<UpdateElementStateRequest>()
        val doc = commander.retrieveDoc(docId) ?: run {
            return call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
        }
        val entity = commander.convertDocToEntity(doc)
        try {
            req.deletedEls?.let {
                commander.updateDeletedElements(docId, deleted = true, it.elIdxes!!, it.relIdxes!!)
            }
            req.undeletedEls?.let {
                commander.updateDeletedElements(docId, deleted = false, it.elIdxes!!, it.relIdxes!!)
            }
            req.usableEls?.let {
                commander.updateUsableElements(docId, usable = true, it.ctIdxes!!, it.elIdxes!!, it.relIdxes!!)
            }
            req.unusableEls?.let {
                commander.updateUsableElements(docId, usable = false, it.ctIdxes!!, it.elIdxes!!, it.relIdxes!!)
            }
            req.renameEls?.forEach {
                when (it.relType) {
                    RelType.Vertex -> commander.renamePointElement(docId, it.relIdx, it.newName)
                    RelType.Line -> commander.renameLineElement(docId, it.relIdx, it.newName)
                }
            }
            commander.replaceDocToDB(doc)
            return call.respond(HttpStatusCode.OK)
        } catch (t: ViException) {
            logger.error("error when update element state... ", t)
            commander.replaceDoc(entity)
            return call.respond(HttpStatusCode.BadRequest, t.message ?: "unknown error")
        } catch (t: Throwable) {
            logger.error("unknown error occurred... ", t)
            commander.replaceDoc(entity)
            return call.respond(HttpStatusCode.InternalServerError, "unknown error")
        }
    }
}
