package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind.PK_Expr
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.impl.constructor.DEFAULT_PRECISION
import viclass.editor.geo.impl.constructor.rotate90Degrees
import viclass.editor.geo.transformer.Transformer

/**
 * calculate length, nth: (AB = length)
 */
class PointOnLineWithLengthTransformer() : Transformer<PointOnLineWithLengthTransformData> {
    override fun apply(
        doc: GeoDoc,
        c: Construction,
        transformData: PointOnLineWithLengthTransformData,
        pos: Vector3D
    ) {
        // Get the root point of the line from transform data
        val rootPoint = Vector3D.of(transformData.root)
        // Get the direction vector of the line from transform data
        val directionVector = Vector3D.of(transformData.unitVector)
        // Create the main line using the root point and direction vector
        val mainLine = Lines3D.fromPointAndDirection(rootPoint, directionVector, DEFAULT_PRECISION)
        // Create a perpendicular line at the given position
        val perpendicularLine =
            Lines3D.fromPointAndDirection(pos, directionVector.rotate90Degrees(), DEFAULT_PRECISION)
        // Find the intersection point between the main line and the perpendicular line
        val intersection = mainLine.intersection(perpendicularLine)
        // Vector from the root point to the intersection
        val vectorToIntersection = rootPoint.vectorTo(intersection)
        // Coefficient to determine direction along the line
        val directionCoefficient = vectorToIntersection.x / directionVector.x

        // Calculate the length of the segment from root to intersection
        val segmentLength = vectorToIntersection.norm()
        // Determine nth value based on direction
        val nthValue = if (directionCoefficient <= 0) 2 else 1

        // Get parameter containers for length and nth value
        val lengthParamContainer = c.params[transformData.lengthParamIdx]
        val nthParamContainer = c.params[transformData.nthParamIdx]

        // Set the length parameter value based on its kind
        if (transformData.lengthParamKind == PK_Value) {
            val lengthParam = lengthParamContainer.specs.getParam(PK_Value) as ParamStoreValue
            lengthParam.value = segmentLength.toString()
        } else if (transformData.lengthParamKind == PK_Expr) {
            val lengthParam = lengthParamContainer.specs.getParam(PK_Expr) as ParamStoreValue
            lengthParam.value = segmentLength.toString()
        }

        // Set the nth parameter value
        val nthParam = nthParamContainer.specs.getParam(PK_Value) as ParamStoreValue
        nthParam.value = nthValue.toString()
    }
}