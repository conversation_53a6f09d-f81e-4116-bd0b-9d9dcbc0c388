import { KeyboardLayout } from './types';

// More keyboard layouts from VSCode:
// https://github.com/microsoft/vscode/tree/main/src/vs/workbench/services/keybinding/browser/keyboardLayouts

export const APPLE_ENGLISH: KeyboardLayout = {
  id: 'apple.en-intl',
  displayName: 'English (international)',
  virtualLayout: 'qwerty',
  platform: 'apple',
  locale: 'en',
  score: 0,
  mapping: {
    KeyA: ['a', 'A', 'å', 'Å'],
    KeyB: ['b', 'B', '∫', 'ı'],
    KeyC: ['c', 'C', 'ç', 'Ç'],
    KeyD: ['d', 'D', '∂', 'Î'],
    KeyE: ['e', 'E', '´', '´'],
    KeyF: ['f', 'F', 'ƒ', 'Ï'],
    KeyG: ['g', 'G', '©', '˝'],
    KeyH: ['h', 'H', '˙', 'Ó'],
    KeyI: ['i', 'I', 'ˆ', 'ˆ'],
    KeyJ: ['j', 'J', '∆', 'Ô'],
    KeyK: ['k', 'K', '˚', ''],
    KeyL: ['l', 'L', '¬', 'Ò'],
    KeyM: ['m', 'M', 'µ', 'Â'],
    KeyN: ['n', 'N', '˜', '˜'],
    KeyO: ['o', 'O', 'ø', 'Ø'],
    KeyP: ['p', 'P', 'π', '∏'],
    KeyQ: ['q', 'Q', 'œ', 'Œ'],
    KeyR: ['r', 'R', '®', '‰'],
    KeyS: ['s', 'S', 'ß', 'Í'],
    KeyT: ['t', 'T', '†', 'ˇ'],
    KeyU: ['u', 'U', '¨', '¨'],
    KeyV: ['v', 'V', '√', '◊'],
    KeyW: ['w', 'W', '∑', '„'],
    KeyX: ['x', 'X', '≈', '˛'],
    KeyY: ['y', 'Y', '¥', 'Á'],
    KeyZ: ['z', 'Z', 'Ω', '¸'],
    Digit1: ['1', '!', '¡', '⁄'],
    Digit2: ['2', '@', '™', '€'],
    Digit3: ['3', '#', '£', '‹'],
    Digit4: ['4', '$', '¢', '›'],
    Digit5: ['5', '%', '∞', 'ﬁ'],
    Digit6: ['6', '^', '§', 'ﬂ'],
    Digit7: ['7', '&', '¶', '‡'],
    Digit8: ['8', '*', '•', '°'],
    Digit9: ['9', '(', 'ª', '·'],
    Digit0: ['0', ')', 'º', '‚'],
    Space: [' ', ' ', ' ', ' '],
    Minus: ['-', '_', '–', '—'],
    Equal: ['=', '+', '≠', '±'],
    BracketLeft: ['[', '{', '“', '”'],
    BracketRight: [']', '}', '‘', '’'],
    Backslash: ['\\', '|', '«', '»'],
    Semicolon: [';', ':', '…', 'Ú'],
    Quote: ["'", '"', 'æ', 'Æ'],
    Backquote: ['`', '˜', '`', '`'],
    Comma: [',', '<', '≤', '¯'],
    Period: ['.', '>', '≥', '˘'],
    Slash: ['/', '?', '÷', '¿'],
    NumpadDivide: ['/', '/', '/', '/'],
    NumpadMultiply: ['*', '*', '*', '*'],
    NumpadSubtract: ['-', '-', '-', '-'],
    NumpadAdd: ['+', '+', '+', '+'],
    Numpad1: ['1', '1', '1', '1'],
    Numpad2: ['2', '2', '2', '2'],
    Numpad3: ['3', '3', '3', '3'],
    Numpad4: ['4', '4', '4', '4'],
    Numpad5: ['5', '5', '5', '5'],
    Numpad6: ['6', '6', '6', '6'],
    Numpad7: ['7', '7', '7', '7'],
    Numpad8: ['8', '8', '8', '8'],
    Numpad9: ['9', '9', '9', '9'],
    Numpad0: ['0', '0', '0', '0'],
    NumpadDecimal: ['.', '.', '.', '.'],
    IntlBackslash: ['§', '±', '§', '±'],
    NumpadEqual: ['=', '=', '=', '='],
    AudioVolumeUp: ['', '=', '', '='],
  },
};

export const WINDOWS_ENGLISH: KeyboardLayout = {
  id: 'windows.en-intl',
  displayName: 'English (international)',
  platform: 'windows',
  virtualLayout: 'qwerty',
  locale: 'en',
  score: 0,
  mapping: {
    KeyA: ['a', 'A', 'á', 'Á'],
    KeyB: ['b', 'B', '', ''],
    KeyC: ['c', 'C', '©', '¢'],
    KeyD: ['d', 'D', 'ð', 'Ð'],
    KeyE: ['e', 'E', 'é', 'É'],
    KeyF: ['f', 'F', '', ''],
    KeyG: ['g', 'G', '', ''],
    KeyH: ['h', 'H', '', ''],
    KeyI: ['i', 'I', 'í', 'Í'],
    KeyJ: ['j', 'J', '', ''],
    KeyK: ['k', 'K', '', ''],
    KeyL: ['l', 'L', 'ø', 'Ø'],
    KeyM: ['m', 'M', 'µ', ''],
    KeyN: ['n', 'N', 'ñ', 'Ñ'],
    KeyO: ['o', 'O', 'ó', 'Ó'],
    KeyP: ['p', 'P', 'ö', 'Ö'],
    KeyQ: ['q', 'Q', 'ä', 'Ä'],
    KeyR: ['r', 'R', '®', ''],
    KeyS: ['s', 'S', 'ß', '§'],
    KeyT: ['t', 'T', 'þ', 'Þ'],
    KeyU: ['u', 'U', 'ú', 'Ú'],
    KeyV: ['v', 'V', '', ''],
    KeyW: ['w', 'W', 'å', 'Å'],
    KeyX: ['x', 'X', '', ''],
    KeyY: ['y', 'Y', 'ü', 'Ü'],
    KeyZ: ['z', 'Z', 'æ', 'Æ'],
    Digit1: ['1', '!', '¡', '¹'],
    Digit2: ['2', '@', '²', ''],
    Digit3: ['3', '#', '³', ''],
    Digit4: ['4', '$', '¤', '£'],
    Digit5: ['5', '%', '€', ''],
    Digit6: ['6', '^', '¼', ''],
    Digit7: ['7', '&', '½', ''],
    Digit8: ['8', '*', '¾', ''],
    Digit9: ['9', '(', '‘', ''],
    Digit0: ['0', ')', '’', ''],
    Space: [' ', ' ', '', ''],
    Minus: ['-', '_', '¥', ''],
    Equal: ['=', '+', '×', '÷'],
    BracketLeft: ['[', '{', '«', ''],
    BracketRight: [']', '}', '»', ''],
    Backslash: ['\\', '|', '¬', '¦'],
    Semicolon: [';', ':', '¶', '°'],
    Quote: ["'", '"', '´', '¨'],
    Backquote: ['`', '~', '', ''],
    Comma: [',', '<', 'ç', 'Ç'],
    Period: ['.', '>', '', ''],
    Slash: ['/', '?', '¿', ''],
    NumpadDivide: ['/', '/', '', ''],
    NumpadMultiply: ['*', '*', '', ''],
    NumpadSubtract: ['-', '-', '', ''],
    NumpadAdd: ['+', '+', '', ''],
    IntlBackslash: ['\\', '|', '', ''],
  },
};

export const LINUX_ENGLISH: KeyboardLayout = {
  id: 'linux.en',
  displayName: 'English',
  platform: 'linux',
  virtualLayout: 'qwerty',
  locale: 'en',
  score: 0,
  mapping: {
    KeyA: ['a', 'A', 'a', 'A'],
    KeyB: ['b', 'B', 'b', 'B'],
    KeyC: ['c', 'C', 'c', 'C'],
    KeyD: ['d', 'D', 'd', 'D'],
    KeyE: ['e', 'E', 'e', 'E'],
    KeyF: ['f', 'F', 'f', 'F'],
    KeyG: ['g', 'G', 'g', 'G'],
    KeyH: ['h', 'H', 'h', 'H'],
    KeyI: ['i', 'I', 'i', 'I'],
    KeyJ: ['j', 'J', 'j', 'J'],
    KeyK: ['k', 'K', 'k', 'K'],
    KeyL: ['l', 'L', 'l', 'L'],
    KeyM: ['m', 'M', 'm', 'M'],
    KeyN: ['n', 'N', 'n', 'N'],
    KeyO: ['o', 'O', 'o', 'O'],
    KeyP: ['p', 'P', 'p', 'P'],
    KeyQ: ['q', 'Q', 'q', 'Q'],
    KeyR: ['r', 'R', 'r', 'R'],
    KeyS: ['s', 'S', 's', 'S'],
    KeyT: ['t', 'T', 't', 'T'],
    KeyU: ['u', 'U', 'u', 'U'],
    KeyV: ['v', 'V', 'v', 'V'],
    KeyW: ['w', 'W', 'w', 'W'],
    KeyX: ['x', 'X', 'x', 'X'],
    KeyY: ['y', 'Y', 'y', 'Y'],
    KeyZ: ['z', 'Z', 'z', 'Z'],
    Digit1: ['1', '!', '1', '!'],
    Digit2: ['2', '@', '2', '@'],
    Digit3: ['3', '#', '3', '#'],
    Digit4: ['4', '$', '4', '$'],
    Digit5: ['5', '%', '5', '%'],
    Digit6: ['6', '^', '6', '^'],
    Digit7: ['7', '&', '7', '&'],
    Digit8: ['8', '*', '8', '*'],
    Digit9: ['9', '(', '9', '('],
    Digit0: ['0', ')', '0', ')'],
    Space: [' ', ' ', ' ', ' '],
    Minus: ['-', '_', '-', '_'],
    Equal: ['=', '+', '=', '+'],
    BracketLeft: ['[', '{', '[', '{'],
    BracketRight: [']', '}', ']', '}'],
    Backslash: ['\\', '|', '\\', '|'],
    Semicolon: [';', ':', ';', ':'],
    Quote: ["'", '"', "'", '"'],
    Backquote: ['`', '~', '`', '~'],
    Comma: [',', '<', ',', '<'],
    Period: ['.', '>', '.', '>'],
    Slash: ['/', '?', '/', '?'],
    NumpadDivide: ['/', '/', '/', '/'],
    NumpadMultiply: ['*', '*', '*', '*'],
    NumpadSubtract: ['-', '-', '-', '-'],
    NumpadAdd: ['+', '+', '+', '+'],
    Numpad1: ['1', '1', '1', '1'],
    Numpad2: ['2', '2', '2', '2'],
    Numpad3: ['3', '3', '3', '3'],
    Numpad4: ['4', '4', '4', '4'],
    Numpad5: ['5', '5', '5', '5'],
    Numpad6: ['6', '6', '6', '6'],
    Numpad7: ['7', '7', '7', '7'],
    Numpad8: ['8', '8', '8', '8'],
    Numpad9: ['9', '9', '9', '9'],
    Numpad0: ['0', '0', '0', '0'],
    NumpadDecimal: ['', '.', '', '.'],
    IntlBackslash: ['<', '>', '|', '¦'],
    NumpadEqual: ['=', '=', '=', '='],
    NumpadComma: ['.', '.', '.', '.'],
    NumpadParenLeft: ['(', '(', '(', '('],
    NumpadParenRight: [')', ')', ')', ')'],
  },
};
