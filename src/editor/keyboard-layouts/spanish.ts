import { KeyboardLayout } from './types';

export const APPLE_SPANISH: KeyboardLayout = {
  id: 'apple.spanish',
  locale: 'es',
  displayName: 'Spanish ISO',
  platform: 'apple',
  virtualLayout: 'qwerty',
  score: 0,
  mapping: {
    KeyA: ['a', 'A', 'å', 'Å'],
    KeyB: ['b', 'B', 'ß', ''],
    KeyC: ['c', 'C', '©', ' '],
    KeyD: ['d', 'D', '∂', '∆'],
    KeyE: ['e', 'E', '€', '€'],
    KeyF: ['f', 'F', 'ƒ', 'ﬁ'],
    KeyG: ['g', 'G', '', 'ﬂ'],
    KeyH: ['h', 'H', '™', ' '],
    KeyI: ['i', 'I', ' ', ' '],
    KeyJ: ['j', 'J', '¶', '¯'],
    KeyK: ['k', 'K', '§', 'ˇ'],
    KeyL: ['l', 'L', ' ', '˘'],
    KeyM: ['m', 'M', 'µ', '˚'],
    KeyN: ['n', 'N', ' ', '˙'],
    KeyO: ['o', 'O', 'ø', 'Ø'],
    KeyP: ['p', 'P', 'π', '∏'],
    KeyQ: ['q', 'Q', 'œ', 'Œ'],
    KeyR: ['r', 'R', '®', ' '],
    KeyS: ['s', 'S', '∫', ' '],
    KeyT: ['t', 'T', '†', '‡'],
    KeyU: ['u', 'U', ' ', ' '],
    KeyV: ['v', 'V', '√', '◊'],
    KeyW: ['w', 'W', 'æ', 'Æ'],
    KeyX: ['x', 'X', '∑', '›'],
    KeyY: ['y', 'Y', '¥', ' '],
    KeyZ: ['z', 'Z', 'Ω', '‹'],
    Digit1: ['1', '!', '|', 'ı'],
    Digit2: ['2', '"', '@', '˝'],
    Digit3: ['3', '·', '#', '•'],
    Digit4: ['4', '$', '¢', '£'],
    Digit5: ['5', '%', '∞', '‰'],
    Digit6: ['6', '&', '¬', ' '],
    Digit7: ['7', '/', '÷', '⁄'],
    Digit8: ['8', '(', '“', '‘'],
    Digit9: ['9', ')', '”', '’'],
    Digit0: ['0', '=', '≠', '≈'],
    Space: [' ', ' ', ' ', ' '],
    Minus: ["'", '?', '´', '¸'],
    Equal: ['¡', '¿', '‚', '˛'],
    BracketLeft: ['`', '^', '[', 'ˆ'],
    BracketRight: ['+', '*', ']', '±'],
    Backslash: ['ç', 'Ç', '}', '»'],
    Semicolon: ['ñ', 'Ñ', '~', '˜'],
    Quote: ['´', '¨', '{', '«'],
    Backquote: ['<', '>', '≤', '≥'],
    Comma: [',', ';', '„', ''],
    Period: ['.', ':', '…', '…'],
    Slash: ['-', '_', '–', '—'],
    NumpadDivide: ['/', '/', '/', '/'],
    NumpadMultiply: ['*', '*', '*', '*'],
    NumpadSubtract: ['-', '-', '-', '-'],
    NumpadAdd: ['+', '+', '+', '+'],
    Numpad1: ['1', '1', '1', '1'],
    Numpad2: ['2', '2', '2', '2'],
    Numpad3: ['3', '3', '3', '3'],
    Numpad4: ['4', '4', '4', '4'],
    Numpad5: ['5', '5', '5', '5'],
    Numpad6: ['6', '6', '6', '6'],
    Numpad7: ['7', '7', '7', '7'],
    Numpad8: ['8', '8', '8', '8'],
    Numpad9: ['9', '9', '9', '9'],
    Numpad0: ['0', '0', '0', '0'],
    NumpadDecimal: [',', ',', ',', ','],
    IntlBackslash: ['º', 'ª', '\\', '°'],
  },
};

export const WINDOWS_SPANISH: KeyboardLayout = {
  id: 'windows.spanish',
  locale: 'es',
  displayName: 'Spanish',
  platform: 'windows',
  virtualLayout: 'qwerty',
  score: 0,
  mapping: {
    KeyA: ['a', 'A', '', ''],
    KeyB: ['b', 'B', '', ''],
    KeyC: ['c', 'C', '', ''],
    KeyD: ['d', 'D', '', ''],
    KeyE: ['e', 'E', '€', ''],
    KeyF: ['f', 'F', '', ''],
    KeyG: ['g', 'G', '', ''],
    KeyH: ['h', 'H', '', ''],
    KeyI: ['i', 'I', '', ''],
    KeyJ: ['j', 'J', '', ''],
    KeyK: ['k', 'K', '', ''],
    KeyL: ['l', 'L', '', ''],
    KeyM: ['m', 'M', '', ''],
    KeyN: ['n', 'N', '', ''],
    KeyO: ['o', 'O', '', ''],
    KeyP: ['p', 'P', '', ''],
    KeyQ: ['q', 'Q', '', ''],
    KeyR: ['r', 'R', '', ''],
    KeyS: ['s', 'S', '', ''],
    KeyT: ['t', 'T', '', ''],
    KeyU: ['u', 'U', '', ''],
    KeyV: ['v', 'V', '', ''],
    KeyW: ['w', 'W', '', ''],
    KeyX: ['x', 'X', '', ''],
    KeyY: ['y', 'Y', '', ''],
    KeyZ: ['z', 'Z', '', ''],
    Digit1: ['1', '!', '|', ''],
    Digit2: ['2', '"', '@', ''],
    Digit3: ['3', '·', '#', ''],
    Digit4: ['4', '$', '~', ''],
    Digit5: ['5', '%', '€', ''],
    Digit6: ['6', '&', '¬', ''],
    Digit7: ['7', '/', '', ''],
    Digit8: ['8', '(', '', ''],
    Digit9: ['9', ')', '', ''],
    Digit0: ['0', '=', '', ''],
    Space: [' ', ' ', '', ''],
    Minus: ["'", '?', '', ''],
    Equal: ['¡', '¿', '', ''],
    BracketLeft: ['`', '^', '[', ''],
    BracketRight: ['+', '*', ']', ''],
    Backslash: ['ç', 'Ç', '}', ''],
    Semicolon: ['ñ', 'Ñ', '', ''],
    Quote: ['´', '¨', '{', ''],
    Backquote: ['º', 'ª', '\\', ''],
    Comma: [',', ';', '', ''],
    Period: ['.', ':', '', ''],
    Slash: ['-', '_', '', ''],
    NumpadDivide: ['/', '/', '', ''],
    NumpadMultiply: ['*', '*', '', ''],
    NumpadSubtract: ['-', '-', '', ''],
    NumpadAdd: ['+', '+', '', ''],
    IntlBackslash: ['<', '>', '', ''],
  },
};
export const LINUX_SPANISH: KeyboardLayout = {
  id: 'linux.spanish',
  locale: 'es',
  displayName: 'Spanish',
  platform: 'linux',
  virtualLayout: 'qwerty',
  score: 0,
  mapping: {
    KeyA: ['a', 'A', 'æ', 'Æ'],
    KeyB: ['b', 'B', '”', '’'],
    KeyC: ['c', 'C', '¢', '©'],
    KeyD: ['d', 'D', 'ð', 'Ð'],
    KeyE: ['e', 'E', '€', '¢'],
    KeyF: ['f', 'F', 'đ', 'ª'],
    KeyG: ['g', 'G', 'ŋ', 'Ŋ'],
    KeyH: ['h', 'H', 'ħ', 'Ħ'],
    KeyI: ['i', 'I', '→', 'ı'],
    KeyJ: ['j', 'J', '̉', '̛'],
    KeyK: ['k', 'K', 'ĸ', '&'],
    KeyL: ['l', 'L', 'ł', 'Ł'],
    KeyM: ['m', 'M', 'µ', 'º'],
    KeyN: ['n', 'N', 'n', 'N'],
    KeyO: ['o', 'O', 'ø', 'Ø'],
    KeyP: ['p', 'P', 'þ', 'Þ'],
    KeyQ: ['q', 'Q', '@', 'Ω'],
    KeyR: ['r', 'R', '¶', '®'],
    KeyS: ['s', 'S', 'ß', '§'],
    KeyT: ['t', 'T', 'ŧ', 'Ŧ'],
    KeyU: ['u', 'U', '↓', '↑'],
    KeyV: ['v', 'V', '“', '‘'],
    KeyW: ['w', 'W', 'ł', 'Ł'],
    KeyX: ['x', 'X', '»', '>'],
    KeyY: ['y', 'Y', '←', '¥'],
    KeyZ: ['z', 'Z', '«', '<'],
    Digit1: ['1', '!', '|', '¡'],
    Digit2: ['2', '"', '@', '⅛'],
    Digit3: ['3', '·', '#', '£'],
    Digit4: ['4', '$', '~', '$'],
    Digit5: ['5', '%', '½', '⅜'],
    Digit6: ['6', '&', '¬', '⅝'],
    Digit7: ['7', '/', '{', '⅞'],
    Digit8: ['8', '(', '[', '™'],
    Digit9: ['9', ')', ']', '±'],
    Digit0: ['0', '=', '}', '°'],
    Enter: ['\r', '\r', '\r', '\r'],
    Escape: ['\u001B', '\u001B', '\u001B', '\u001B'],
    Backspace: ['\b', '\b', '\b', '\b'],
    Tab: ['\t', '', '\t', ''],
    Space: [' ', ' ', ' ', ' '],
    Minus: ["'", '?', '\\', '¿'],
    Equal: ['¡', '¿', '̃', '~'],
    BracketLeft: ['̀', '̂', '[', '̊'],
    BracketRight: ['+', '*', ']', '̄'],
    Backslash: ['ç', 'Ç', '}', '̆'],
    Semicolon: ['ñ', 'Ñ', '~', '̋'],
    Quote: ['́', '̈', '{', '{'],
    Backquote: ['º', 'ª', '\\', '\\'],
    Comma: [',', ';', '─', '×'],
    Period: ['.', ':', '·', '÷'],
    Slash: ['-', '_', '̣', '̇'],
    NumpadDivide: ['/', '/', '/', '/'],
    NumpadMultiply: ['*', '*', '*', '*'],
    NumpadSubtract: ['-', '-', '-', '-'],
    NumpadAdd: ['+', '+', '+', '+'],
    NumpadEnter: ['\r', '\r', '\r', '\r'],
    Numpad1: ['', '1', '', '1'],
    Numpad2: ['', '2', '', '2'],
    Numpad3: ['', '3', '', '3'],
    Numpad4: ['', '4', '', '4'],
    Numpad5: ['', '5', '', '5'],
    Numpad6: ['', '6', '', '6'],
    Numpad7: ['', '7', '', '7'],
    Numpad8: ['', '8', '', '8'],
    Numpad9: ['', '9', '', '9'],
    Numpad0: ['', '0', '', '0'],
    NumpadDecimal: ['', '.', '', '.'],
    IntlBackslash: ['<', '>', '|', '¦'],
    NumpadEqual: ['=', '=', '=', '='],
    NumpadComma: ['.', '.', '.', '.'],
    NumpadParenLeft: ['(', '(', '(', '('],
    NumpadParenRight: [')', ')', ')', ')'],
  },
};
