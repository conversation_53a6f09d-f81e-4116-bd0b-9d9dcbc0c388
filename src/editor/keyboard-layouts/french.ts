import { KeyboardLayout } from './types';

export const APPLE_FRENCH: KeyboardLayout = {
  id: 'apple.french',
  locale: 'fr',
  displayName: 'French',
  platform: 'apple',
  virtualLayout: 'azerty',
  score: 0,
  mapping: {
    KeyA: ['q', 'Q', '‡', 'Ω'],
    KeyB: ['b', 'B', 'ß', '∫'],
    KeyC: ['c', 'C', '©', '¢'],
    KeyD: ['d', 'D', '∂', '∆'],
    KeyE: ['e', 'E', 'ê', 'Ê'],
    KeyF: ['f', 'F', 'ƒ', '·'],
    KeyG: ['g', 'G', 'ﬁ', 'ﬂ'],
    KeyH: ['h', 'H', 'Ì', 'Î'],
    KeyI: ['i', 'I', 'î', 'ï'],
    KeyJ: ['j', 'J', 'Ï', 'Í'],
    KeyK: ['k', 'K', 'È', 'Ë'],
    KeyL: ['l', 'L', '¬', '|'],
    KeyM: [',', '?', '∞', '¿'],
    KeyN: ['n', 'N', '~', 'ı'],
    KeyO: ['o', 'O', 'œ', 'Œ'],
    KeyP: ['p', 'P', 'π', '∏'],
    KeyQ: ['a', 'A', 'æ', 'Æ'],
    KeyR: ['r', 'R', '®', '‚'],
    KeyS: ['s', 'S', 'Ò', '∑'],
    KeyT: ['t', 'T', '†', '™'],
    KeyU: ['u', 'U', 'º', 'ª'],
    KeyV: ['v', 'V', '◊', '√'],
    KeyW: ['z', 'Z', 'Â', 'Å'],
    KeyX: ['x', 'X', '≈', '⁄'],
    KeyY: ['y', 'Y', 'Ú', 'Ÿ'],
    KeyZ: ['w', 'W', '‹', '›'],
    Digit1: ['&', '1', '', '´'],
    Digit2: ['é', '2', 'ë', '„'],
    Digit3: ['"', '3', '“', '”'],
    Digit4: ["'", '4', '‘', '’'],
    Digit5: ['(', '5', '{', '['],
    Digit6: ['§', '6', '¶', 'å'],
    Digit7: ['è', '7', '«', '»'],
    Digit8: ['!', '8', '¡', 'Û'],
    Digit9: ['ç', '9', 'Ç', 'Á'],
    Digit0: ['à', '0', 'ø', 'Ø'],
    Space: [' ', ' ', ' ', ' '],
    Minus: [')', '°', '}', ']'],
    Equal: ['-', '_', '—', '–'],
    BracketLeft: ['^', '¨', 'ô', 'Ô'],
    BracketRight: ['$', '*', '€', '¥'],
    Backslash: ['`', '£', '@', '#'],
    Semicolon: ['m', 'M', 'µ', 'Ó'],
    Quote: ['ù', '%', 'Ù', '‰'],
    Backquote: ['<', '>', '≤', '≥'],
    Comma: [';', '.', '…', '•'],
    Period: [':', '/', '÷', '\\'],
    Slash: ['=', '+', '≠', '±'],
    NumpadDivide: ['/', '/', '/', '/'],
    NumpadMultiply: ['*', '*', '*', '*'],
    NumpadSubtract: ['-', '-', '-', '-'],
    NumpadAdd: ['+', '+', '+', '+'],
    NumpadDecimal: [',', '.', ',', '.'],
    IntlBackslash: ['@', '#', '•', 'Ÿ'],
    NumpadEqual: ['=', '=', '=', '='],
  },
};

export const WINDOWS_FRENCH: KeyboardLayout = {
  id: 'windows.french',
  locale: 'fr',
  displayName: 'French',
  virtualLayout: 'azerty',
  platform: 'windows',
  score: 0,
  mapping: {
    KeyA: ['q', 'Q', '', ''],
    KeyB: ['b', 'B', '', ''],
    KeyC: ['c', 'C', '', ''],
    KeyD: ['d', 'D', '', ''],
    KeyE: ['e', 'E', '€', ''],
    KeyF: ['f', 'F', '', ''],
    KeyG: ['g', 'G', '', ''],
    KeyH: ['h', 'H', '', ''],
    KeyI: ['i', 'I', '', ''],
    KeyJ: ['j', 'J', '', ''],
    KeyK: ['k', 'K', '', ''],
    KeyL: ['l', 'L', '', ''],
    KeyM: [',', '?', '', ''],
    KeyN: ['n', 'N', '', ''],
    KeyO: ['o', 'O', '', ''],
    KeyP: ['p', 'P', '', ''],
    KeyQ: ['a', 'A', '', ''],
    KeyR: ['r', 'R', '', ''],
    KeyS: ['s', 'S', '', ''],
    KeyT: ['t', 'T', '', ''],
    KeyU: ['u', 'U', '', ''],
    KeyV: ['v', 'V', '', ''],
    KeyW: ['z', 'Z', '', ''],
    KeyX: ['x', 'X', '', ''],
    KeyY: ['y', 'Y', '', ''],
    KeyZ: ['w', 'W', '', ''],
    Digit1: ['&', '1', '', ''],
    Digit2: ['é', '2', '~', ''],
    Digit3: ['"', '3', '#', ''],
    Digit4: ["'", '4', '{', ''],
    Digit5: ['(', '5', '[', ''],
    Digit6: ['-', '6', '|', ''],
    Digit7: ['è', '7', '`', ''],
    Digit8: ['_', '8', '\\', ''],
    Digit9: ['ç', '9', '^', ''],
    Digit0: ['à', '0', '@', ''],
    Space: [' ', ' ', '', ''],
    Minus: [')', '°', ']', ''],
    Equal: ['=', '+', '}', ''],
    BracketLeft: ['^', '¨', '', ''],
    BracketRight: ['$', '£', '¤', ''],
    Backslash: ['*', 'µ', '', ''],
    Semicolon: ['m', 'M', '', ''],
    Quote: ['ù', '%', '', ''],
    Backquote: ['²', '', '', ''],
    Comma: [';', '.', '', ''],
    Period: [':', '/', '', ''],
    Slash: ['!', '§', '', ''],
    NumpadDivide: ['/', '/', '', ''],
    NumpadMultiply: ['*', '*', '', ''],
    NumpadSubtract: ['-', '-', '', ''],
    NumpadAdd: ['+', '+', '', ''],
    IntlBackslash: ['<', '>', '', ''],
  },
};

export const LINUX_FRENCH: KeyboardLayout = {
  id: 'linux.french',
  locale: 'fr',
  displayName: 'French',
  virtualLayout: 'azerty',
  platform: 'linux',
  score: 0,
  mapping: {
    KeyA: ['q', 'Q', '@', 'Ω'],
    KeyB: ['b', 'B', '”', '’'],
    KeyC: ['c', 'C', '¢', '©'],
    KeyD: ['d', 'D', 'ð', 'Ð'],
    KeyE: ['e', 'E', '€', '¢'],
    KeyF: ['f', 'F', 'đ', 'ª'],
    KeyG: ['g', 'G', 'ŋ', 'Ŋ'],
    KeyH: ['h', 'H', 'ħ', 'Ħ'],
    KeyI: ['i', 'I', '→', 'ı'],
    KeyJ: ['j', 'J', '̉', '̛'],
    KeyK: ['k', 'K', 'ĸ', '&'],
    KeyL: ['l', 'L', 'ł', 'Ł'],
    KeyM: [',', '?', '́', '̋'],
    KeyN: ['n', 'N', 'n', 'N'],
    KeyO: ['o', 'O', 'ø', 'Ø'],
    KeyP: ['p', 'P', 'þ', 'Þ'],
    KeyQ: ['a', 'A', 'æ', 'Æ'],
    KeyR: ['r', 'R', '¶', '®'],
    KeyS: ['s', 'S', 'ß', '§'],
    KeyT: ['t', 'T', 'ŧ', 'Ŧ'],
    KeyU: ['u', 'U', '↓', '↑'],
    KeyV: ['v', 'V', '“', '‘'],
    KeyW: ['z', 'Z', '«', '<'],
    KeyX: ['x', 'X', '»', '>'],
    KeyY: ['y', 'Y', '←', '¥'],
    KeyZ: ['w', 'W', 'ł', 'Ł'],
    Digit1: ['&', '1', '¹', '¡'],
    Digit2: ['é', '2', '~', '⅛'],
    Digit3: ['"', '3', '#', '£'],
    Digit4: ["'", '4', '{', '$'],
    Digit5: ['(', '5', '[', '⅜'],
    Digit6: ['-', '6', '|', '⅝'],
    Digit7: ['è', '7', '`', '⅞'],
    Digit8: ['_', '8', '\\', '™'],
    Digit9: ['ç', '9', '^', '±'],
    Digit0: ['à', '0', '@', '°'],
    Enter: ['\r', '\r', '\r', '\r'],
    Escape: ['\u001B', '\u001B', '\u001B', '\u001B'],
    Backspace: ['\b', '\b', '\b', '\b'],
    Tab: ['\t', '', '\t', ''],
    Space: [' ', ' ', ' ', ' '],
    Minus: [')', '°', ']', '¿'],
    Equal: ['=', '+', '}', '̨'],
    BracketLeft: ['̂', '̈', '̈', '̊'],
    BracketRight: ['$', '£', '¤', '̄'],
    Backslash: ['*', 'µ', '̀', '̆'],
    Semicolon: ['m', 'M', 'µ', 'º'],
    Quote: ['ù', '%', '̂', '̌'],
    Backquote: ['²', '~', '¬', '¬'],
    Comma: [';', '.', '─', '×'],
    Period: [':', '/', '·', '÷'],
    Slash: ['!', '§', '̣', '̇'],
    NumpadMultiply: ['*', '*', '*', '*'],
    NumpadSubtract: ['-', '-', '-', '-'],
    NumpadAdd: ['+', '+', '+', '+'],
    NumpadDecimal: ['', '.', '', '.'],
    IntlBackslash: ['<', '>', '|', '¦'],
  },
};
