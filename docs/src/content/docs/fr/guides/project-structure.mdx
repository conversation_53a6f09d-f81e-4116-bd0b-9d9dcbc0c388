---
title: Structure du projet
description: Apprenez à organiser les fichiers dans votre projet Starlight.
---

Ce guide vous montrera comment un projet Starlight est organisé et ce que font les différents fichiers de votre projet.

Les projets Starlight suivent généralement la même structure de fichiers et de répertoires que les autres projets Astro. Voir [la documentation sur la structure des projets Astro](https://docs.astro.build/fr/core-concepts/project-structure/) pour plus de détails.

## Fichiers et répertoires

- `astro.config.mjs` — Le fichier de configuration d'Astro ; inclut l'intégration et la configuration de Starlight.
- `src/content/config.ts` — Fichier de configuration des collections de contenu ; ajoute les schémas de la matière première de Starlight à votre projet.
- `src/content/docs/` — Fichiers de contenu. Starlight transforme chaque fichier `.md`, `.mdx` ou `.mdoc` de ce répertoire en une page de votre site.
- `src/content/i18n/` (optionnel) — Données de traduction pour prendre en charge l'[internationalisation](/fr/guides/i18n/).
- `src/` — Autre code source et fichiers (composants, styles, images, etc.) pour votre projet.
- `public/` — Actifs statiques (polices, favicon, PDFs, etc.) qui ne seront pas traités par Astro.

## Exemple de contenu de projet

Un répertoire de projet Starlight peut ressembler à ceci :

import { FileTree } from '@astrojs/starlight/components';

<FileTree>

- public/
  - favicon.svg
- src/
  - assets/
    - logo.svg
    - screenshot.jpg
  - components/
    - CustomButton.astro
    - InteractiveWidget.jsx
  - content/
    - docs/
      - guides/
        - 01-getting-started.md
        - 02-advanced.md
      - index.mdx
    - config.ts
  - env.d.ts
- astro.config.mjs
- package.json
- tsconfig.json

</FileTree>
