---
title: Starlight 🌟 Створюйте документаційні сайти з Astro
head:
  - tag: title
    content: Starlight 🌟 Створюйте документаційні сайти з Astro
description: Starlight допомагає створювати гарні й високоефективні документаційні вебсайти за допомогою Astro.
template: splash
editUrl: false
lastUpdated: false
hero:
  title: Осяйте вашу документацію зі Starlight
  tagline: Усе, що треба для створення блискучого документаційного сайту. Швидко, доступно та просто в користуванні.
  image:
    file: ~/assets/hero-star.webp
  actions:
    - text: Розпочати
      icon: right-arrow
      variant: primary
      link: /uk/getting-started/
    - text: Переглянути на GitHub
      icon: external
      link: https://github.com/withastro/starlight
---

import { CardGrid, Card } from '@astrojs/starlight/components';
import AboutAstro from '~/components/about-astro.astro';

<CardGrid stagger>
	<Card title="Документація, що кидає в захват" icon="open-book">
		Містить: навіґацію по сайту, пошук, інтернаціоналізацію, SEO, читку
		типоґрафіку, підсвічування коду, темний режим і багато іншого.
	</Card>
	<Card title="Працює на Astro" icon="rocket">
		Пустіть усю міць і швидкість Astro в дію. Розширте можливості Starlight за
		допомогою ваших улюблених інтеґрацій та бібліотек для Astro.
	</Card>
	<Card title="Markdown, Markdoc і MDX" icon="document">
		Оберіть свою улюблену мову розмітки. Starlight забезпечить типобезпечну
		валідацію frontmatter-преамбули за підтримки TypeScript.
	</Card>
	<Card title="Заживайте власні UI-компоненти" icon="puzzle">
		Starlight це цілісне й безстороннє рішення, поєднуване з React, Vue, Svelte,
		Solid тощо.
	</Card>
</CardGrid>

<AboutAstro title="Подано">
Astro ─ це універсальний веб-фреймворк, наготований на швидкість.
Беріть контент хоч звідки та розгортайте його будь-де, користуючи з ваших улюблених UI-компонентів і бібліотек.

[Дізнатися більше про Astro](https://astro.build/)

</AboutAstro>
