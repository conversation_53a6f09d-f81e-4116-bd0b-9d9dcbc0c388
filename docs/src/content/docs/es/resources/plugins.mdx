---
title: Plugins e Integraciones
description: ¡Descubre herramientas de la comunidad como plugins e integraciones que amplían Starlight!
sidebar:
  order: 1
---

:::tip[¡Agrega el tuyo!]
¿Has construido un plugin o herramienta para Starlight?
¡Abre una PR añadiendo un enlace a esta página!
:::

## Plugins

Los [Plugins](/es/reference/plugins/) pueden personalizar la configuración, UI y comportamiento de Starlight, a la vez que son fáciles de compartir y reutilizar.
Amplia tu sitio con un plugins oficiales respaldados por el equipo de Starlight y plugins de la comunidad mantenidos por usuario de Starlight.

### Plugins oficiales

<CardGrid>
	<LinkCard
		href="/es/guides/site-search/#algolia-docsearch"
		title="Algolia DocSearch"
		description="Reemplaza Pagefind, el proveedor de búsqueda por defecto, por Algolia DocSearch."
	/>
</CardGrid>

### Plugins de la comunidad

<CardGrid>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-links-validator"
		title="starlight-links-validator"
		description="Comprueba si hay enlaces rotos en tus páginas de Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-typedoc"
		title="starlight-typedoc"
		description="Genera páginas de Starlight desde TypeScript usando TypeDoc"
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-blog"
		title="starlight-blog"
		description="Agrega un blog a tu sitio de documentación."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-openapi"
		title="starlight-openapi"
		description="Crea páginas de documentación a partir de especificaciones OpenAPI/Swagger."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-obsidian"
		title="starlight-obsidian"
		description="Publica bóvedas de Obsidian en tu sitio de Starlight."
	/>
	<LinkCard
		href="https://astro-ghostcms.xyz/intro/starlight/install/"
		title="starlight-ghostcms"
		description="Agrega tus publicaciones de blog de GhostCMS junto con tus documentos de Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-image-zoom"
		title="starlight-image-zoom"
		description="Agrega capacidades de zoom a las imágenes de tu documentación."
	/>
	<LinkCard
		href="https://github.com/lorenzolewis/starlight-utils"
		title="starlight-utils"
		description="Extiende Starlight con una colección de utilidades comunes."
	/>
	<LinkCard
		href="https://github.com/trueberryless/starlight-view-modes"
		title="starlight-view-modes"
		description="Agrega diferentes capacidades de modo de vista a tu sitio de documentación."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-versions"
		title="starlight-versions"
		description="Agrega versiones a tus páginas de documentación de Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-theme-rapide"
		title="starlight-theme-rapide"
		description="Tema de Starlight inspirado en el tema Vitesse de Visual Studio Code."
	/>
</CardGrid>

## Herramientas e integraciones de la comunidad

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

Estas herramientas e integraciones de la comunidad se pueden utilizar para añadir características a tu sitio de Starlight.

<CardGrid>
	<LinkCard
		href="https://www.feelback.dev/blog/new-astro-starlight-integration/"
		title="FeelBack"
		description="Agrega un sistema de comentarios de usuarios a la páginas de tus docs."
	/>
	<LinkCard
		href="https://github.com/val-town/notion-to-astro"
		title="notion-to-astro"
		description="Convierte las exportaciones de Notion a docs de Astro Starlight."
	/>
	<LinkCard
		href="https://github.com/mattjennings/astro-live-code"
		title="astro-live-code"
		description="Renderiza bloques de código MDX como componentes interactivos."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-i18n"
		title="starlight-i18n"
		description="Extensión de Visual Studio Code para ayudar a traducir páginas de Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-package-managers"
		title="starlight-package-managers"
		description="Muestra rápidamente comandos relacionados con npm para varios gestores de paquetes."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-showcases"
		title="starlight-showcases"
		description="Conjunto de componentes de Starlight para crear páginas de vitrina."
	/>
</CardGrid>
