---
title: <PERSON><PERSON><PERSON>
description: Lerne wie du deine nächste Dokumentationsseite mit Starlight und Astro erstellst.
---

import { Tabs, TabItem } from '@astrojs/starlight/components';

## Erstelle ein neues Projekt

Starlight ist ein voll ausgestattetes Dokumentations-Theme, das auf dem [Astro](https://astro.build) Framework aufbaut.

Du kannst ein neues Astro + Starlight Projekt mit dem folgenden Befehl erstellen:

<Tabs>
<TabItem label="npm">

```sh
# Erstelle ein neues Projekt mit npm
npm create astro@latest -- --template starlight
```

</TabItem>
<TabItem label="pnpm">

```sh
# Erstelle ein neues Projekt mit pnpm
pnpm create astro --template starlight
```

</TabItem>
<TabItem label="Yarn">

```sh
# Erstelle ein neues Projekt mit yarn
yarn create astro --template starlight
```

</TabItem>
</Tabs>

Damit wird ein neues [Projektverzeichnis](/de/guides/project-structure/) mit allen erforderlichen Dateien und Konfigurationen für deine Website erstellt.

:::tip[In Aktion sehen]
Probiere Starlight in deinem Browser aus:
[öffne die Vorlage in StackBlitz](https://stackblitz.com/github/withastro/starlight/tree/main/examples/basics).
:::

## Inhalte mit Starlight erstellen

Starlight ist bereit für dich, neuen Inhalt hinzuzufügen oder deine vorhandenen Dateien mitzubringen!

### Dateiformate

Starlight unterstützt das Erstellen von Inhalten in Markdown und MDX. (Du kannst die experimentelle [Astro Markdoc Integration](https://docs.astro.build/de/guides/integrations-guide/markdoc/) installieren, um Markdoc zu unterstützen.)

### Seiten hinzufügen

Füge neue Seiten zu deiner Website automatisch hinzu, indem du `.md` oder `.mdx` Dateien in `src/content/docs/` erstellst. Erstelle Unterordner, um deine Dateien zu organisieren und mehrere Pfadsegmente zu erstellen:

```
src/content/docs/hello-world.md => your-site.com/hello-world
src/content/docs/guides/faq.md => your-site.com/guides/faq
```

### Typsichere Frontmatter

Alle Starlight Seiten teilen sich anpassbare [Frontmatter-Eigenschaften](/de/reference/frontmatter/), mit denen das Erscheinungsbild der Seite gesteuert wird:

```md
---
title: Hello, World!
description: This is a page in my Starlight-powered site
---
```

Wenn du etwas Wichtiges vergisst, wird Starlight dich daran erinnern.

## Veröffentlichung deiner Starlight-Website

Sobald du deine Starlight Website erstellt und angepasst hast, kannst du sie auf einen Webserver oder Hosting-Plattform deiner Wahl veröffentlichen, einschließlich Netlify, Vercel, GitHub Pages und vielen mehr.

[Lerne mehr über die Veröffentlichung einer Astro-Website in der Astro-Dokumentation.](https://docs.astro.build/de/guides/deploy/)

## Starlight aktualisieren

:::tip[Tipp]
Da es sich bei Starlight um eine Beta-Software handelt, wird es regelmäßig Updates und Verbesserungen geben. Achte darauf Starlight regelmäßig zu aktualisieren!
:::

Starlight ist eine Astro-Integration und wird wie jede `@astrojs/*`-Integration aktualisiert:

<Tabs>
<TabItem label="npm">

```sh
# Starlight mit npm aktualisieren
npm install @astrojs/starlight@latest
```

</TabItem>
<TabItem label="pnpm">

```sh
# Starlight mit pnpm aktualisieren
pnpm upgrade @astrojs/starlight --latest
```

</TabItem>
<TabItem label="Yarn">

```sh
# Starlight mit yarn aktualisieren
yarn upgrade @astrojs/starlight --latest
```

</TabItem>
</Tabs>

Eine vollständige Liste der Änderungen findest du im [Starlight Changelog](https://github.com/withastro/starlight/blob/main/packages/starlight/CHANGELOG.md).

## Fehlerbehebung

Sowohl die Starlight [Projektkonfiguration](/de/reference/configuration/) als auch die [Konfiguration einzelner Seiten](/de/reference/frontmatter/) findest du im Referenzbereich dieser Website. Nutze diese Seiten, um sicherzustellen, dass deine Starlight-Site richtig konfiguriert ist und funktioniert.

In der wachsenden Liste der Anleitungen in der Seitenleiste findest du Hilfe beim Hinzufügen von Inhalten und beim Anpassen deiner Starlight-Website.

Wenn du keine Antwort in dieser Dokumentation finden kannst, besuche bitte die [Astro Docs](https://docs.astro.build/de/) für die vollständige Astro-Dokumentation. Deine Frage kann vielleicht beantwortet werden, wenn du verstehst, wie Astro im Allgemeinen funktioniert.

Du kannst auch nach bekannten [Starlight-Problemen auf GitHub](https://github.com/withastro/starlight/issues) suchen und im [Astro Discord](https://astro.build/chat/) Hilfe von unserer aktiven, freundlichen Community erhalten! Du kannst Fragen in unserem `#Support`-Forum stellen oder unseren speziellen `#starlight`-Channel besuchen, um aktuelle Entwicklungen und mehr zu diskutieren.
