import { EditorBackendConnector } from '@viclass/editor.core';
import axios, { ResponseType } from 'axios';

export class DirectBackendConnector implements EditorBackendConnector {
    constructor(private apiUrl: string) {}

    loadDocumentByLocalId(channelCode: number, localId: number, responseType: ResponseType): Promise<any> {
        throw new Error('Method not implemented');
    }

    reloadDocumentByLocalId(channelCode: number, localId: number, responseType: ResponseType): Promise<any> {
        throw new Error('Method not implemented.');
    }

    /**
     * by convention, all API for loading document is at /document/fetch
     */
    loadDocumentByGlobalId(channelCode: number, globalId: string, responseType: ResponseType): Promise<Uint8Array> {
        return axios
            .get(`${this.apiUrl}/document/fetch`, {
                params: {
                    globalId: globalId,
                },
                responseType: responseType,
            })
            .then(
                resp => resp.data,
                err => {
                    throw err;
                }
            );
    }
}
