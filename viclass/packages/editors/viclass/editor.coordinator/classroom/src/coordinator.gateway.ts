import {
    ClassRoomCoordinatorState,
    ClassroomInfo,
    ClassroomLocalContentState,
    DefaultSetting,
    PresenterState,
} from './data.model';
import axios, { AxiosError, AxiosInstance, AxiosResponse, ResponseType } from 'axios';
import {
    delayPromise,
    DocLocalContent,
    DocLocalId,
    DocumentId,
    EditorBackendConnector,
    ErrorHandlerDecorator,
    Position,
    ViewportId,
} from '@viclass/editor.core';
import { SignalMessage } from './signal.message';
import { ClassroomCriticalError, ClassroomNonCriticalError } from './classroom.error.model';
import { coordErrorHandler } from './error.handler';

/**
 * Implements the `EditorBackendConnector` interface specifically for the classroom context.
 * It uses the `CoordinatorGateway` to fetch document data based on coordinator state IDs.
 */
export class ClassroomEditorBackendConnector implements EditorBackendConnector {
    protected MAX_RETRY_LOAD_RESOURCE = 10;
    /**
     * Creates an instance of ClassroomEditorBackendConnector.
     * @param coordinatorGateway - The gateway instance to use for backend communication.
     * @param coordStateId - The specific coordinator state ID this connector is associated with.
     */
    constructor(
        private coordinatorGateway: CoordinatorGateway,
        private coordStateId: ViewportId
    ) {}

    /**
     * Loads document data using its local ID within the context of the associated coordinator state.
     * @param channelCode - The editor channel code.
     * @param localId - The document's local ID.
     * @param responseType - The expected response type (e.g., 'json', 'arraybuffer').
     * @param retry
     * @returns A promise resolving to the document data.
     */
    @ErrorHandlerDecorator([coordErrorHandler])
    async loadDocumentByLocalId(
        channelCode: number,
        localId: DocLocalId,
        responseType: ResponseType,
        retry: number = 0
    ): Promise<any> {
        try {
            return await this.coordinatorGateway.fetchDocumentByLocalId(
                this.coordStateId,
                channelCode,
                localId,
                responseType
            );
        } catch (err) {
            console.error('Error loading doc by local id! Retry ', retry, err);
            if (err instanceof ClassroomNonCriticalError) {
                if (retry < this.MAX_RETRY_LOAD_RESOURCE) {
                    await delayPromise(1000);
                    return this.loadDocumentByLocalId(channelCode, localId, responseType, retry + 1);
                }
            }
            throw err;
        }
    }

    /**
     * Reloads document data using its local ID.
     * @param channelCode - The editor channel code.
     * @param docLocalId
     * @param responseType - The expected response type.
     * @returns A promise resolving to the reloaded document data.
     * @throws Error - Method currently not implemented.
     */
    reloadDocumentByLocalId(channelCode: number, docLocalId: DocLocalId, responseType: ResponseType): Promise<any> {
        // TODO: Implement reload logic if different from loadDocumentByLocalId
        throw new Error('Method reloadDocumentByLocalId not implemented.');
    }

    /**
     * Loads document data using its global ID.
     * @param channelCode - The editor channel code.
     * @param globalId - The document's global ID.
     * @param responseType - The expected response type.
     * @param retry
     * @returns A promise resolving to the document data.
     */
    @ErrorHandlerDecorator([coordErrorHandler])
    async loadDocumentByGlobalId(
        channelCode: number,
        globalId: DocumentId,
        responseType: ResponseType,
        retry: number = 0
    ): Promise<any> {
        try {
            return await this.coordinatorGateway.fetchDocumentByGlobalId(channelCode, globalId, responseType);
        } catch (err) {
            console.error('Error loading doc by global id! Retry ', retry, err);
            if (err instanceof ClassroomNonCriticalError) {
                if (retry < this.MAX_RETRY_LOAD_RESOURCE) {
                    await delayPromise(1000);
                    return this.loadDocumentByGlobalId(channelCode, globalId, responseType, retry + 1);
                }
            }
            throw err;
        }
    }
}

/**
 * Provides methods to interact with the backend Coordinator Service (CCS).
 * Handles API requests for signaling, state management, document fetching, etc.
 * Includes an Axios interceptor for centralized error handling and logging.
 *
 * <AUTHOR>
 */
export class CoordinatorGateway {
    /** Custom Axios instance for communication with the CCS backend. */
    private api: AxiosInstance;

    /**
     * Creates an instance of CoordinatorGateway.
     * @param cssEndPoint - The base URL of the Coordinator Service (CCS).
     */
    constructor(private cssEndPoint: string) {
        // Create a dedicated Axios instance with baseURL and default config
        this.api = axios.create({
            baseURL: this.cssEndPoint, // Set base URL for all requests from this instance
            withCredentials: true, // Send cookies with cross-origin requests
            // timeout: 30000, // Default request timeout (e.g., 30 seconds)
        });

        // --- Attach Response Interceptor to this specific Axios instance ---
        this.api.interceptors.response.use(
            (response: AxiosResponse) => {
                // --- Success Handling (Optional) ---
                // You could add logging or data transformation here if needed
                // console.debug(`Request to ${response.config.url} successful (Status: ${response.status})`);
                return response; // Forward successful response
            },
            (error: AxiosError) => {
                // --- Error Handling ---
                console.error('Axios Error Interceptor (CoordinatorGateway) caught an error:', error.message);

                const config = error.config;
                // Construct full URL for better logging
                const requestUrl = config ? `${config.baseURL || ''}${config.url || ''}` : 'Unknown URL';

                if (error.response) {
                    // --- Server responded with an error status code (non-2xx) ---
                    const status = error.response.status;
                    const data: any = error.response.data; // Use 'any' for flexibility or define a type
                    const errorCode = data?.errorCode || data?.code; // Look for common error code fields

                    console.error(`HTTP Error: ${status} on URL: ${requestUrl}`, 'Response Data:', data);

                    // Handle specific status codes
                    switch (status) {
                        case 400: // Bad Request
                            this.showUserLogMessage(`Invalid request to ${requestUrl}. Please check input.`, 'error');
                            // Potentially return a specific error type based on errorCode
                            return Promise.reject(
                                new ClassroomNonCriticalError(
                                    { type: 'BAD_REQUEST', data: true },
                                    'Lỗi dữ liệu không hợp lệ!',
                                    error
                                )
                            );
                        case 401: // Unauthorized
                            console.warn(`Unauthorized access to ${requestUrl}. Redirecting or needs token refresh...`);
                            this.showUserLogMessage('Authentication required. Please log in again.', 'warning');
                            // return Promise.reject(
                            //     new ClassroomCriticalError(
                            //         { type: 'PEER_KICKED_OUT', data: true },
                            //         'Vui lòng đăng nhập lại tài khoản.',
                            //         error
                            //     )
                            // );
                            break;
                        case 403: // Forbidden
                            this.showUserLogMessage(`Permission denied for resource: ${requestUrl}.`, 'warning');
                            if (errorCode === 'OFFLINE_USER') {
                                return Promise.reject(
                                    new ClassroomCriticalError(
                                        { type: 'OFFLINE_USER', data: true },
                                        'Học viên đã rời khỏi lớp',
                                        error
                                    )
                                );
                            }
                            // Handle specific backend error codes for 403
                            if (errorCode === 'CLASS_NOT_OPENED') {
                                return Promise.reject(
                                    new ClassroomCriticalError(
                                        { type: 'CLASS_NOT_OPENED', data: true },
                                        'Lớp học chưa được mở',
                                        error
                                    )
                                );
                            }
                            if (errorCode === 'CLASS_ENDED') {
                                return Promise.reject(
                                    new ClassroomCriticalError(
                                        { type: 'CLASS_ENDED', data: true },
                                        'Lớp học đã kết thúc',
                                        error
                                    )
                                );
                            }
                            if (errorCode === 'PEER_KICKED_OUT') {
                                return Promise.reject(
                                    new ClassroomCriticalError(
                                        { type: 'PEER_KICKED_OUT', data: true },
                                        'Người dùng đã rời khỏi lớp học',
                                        error
                                    )
                                );
                            }
                            if (errorCode === 'PEER_INVALID') {
                                // This might indicate the peer ID is no longer valid on the server
                                return Promise.reject(
                                    new ClassroomCriticalError(
                                        { type: 'CONNECTION_ERROR', data: true },
                                        'Lỗi kết nối, vui lòng tải lại trang.',
                                        error
                                    )
                                );
                            }
                            return Promise.reject(
                                new ClassroomNonCriticalError(
                                    { type: 'UNCLASSIFIED', data: true },
                                    'Bạn không có quyền thực hiện hành động này.',
                                    error
                                )
                            );
                        case 404: // Not Found
                            this.showUserLogMessage('The requested resource could not be found.', 'warning');
                            const contentType = error.response.headers['content-type'];
                            if (contentType?.includes('text/plain') && typeof data === 'string') {
                                console.log('404 do backend chủ động trả về:', data);
                                return Promise.reject(
                                    new ClassroomNonCriticalError(
                                        { type: 'RESOURCE_NOTFOUND', data: true },
                                        'Không tìm thấy tài nguyên!.',
                                        error
                                    )
                                );
                            } else {
                                console.log('404 do route không tồn tại (hoặc web server trả về)');
                                return Promise.reject(
                                    new ClassroomCriticalError(
                                        { type: 'RESOURCE_NOTFOUND', data: true },
                                        'Đường dẫn không tồn tại!.',
                                        error
                                    )
                                );
                            }
                        case 412: // Precondition Failed
                            this.showUserLogMessage('The requested resource could not be found.', 'warning');
                            return Promise.reject(
                                new ClassroomCriticalError(
                                    { type: 'UNCLASSIFIED', data: true },
                                    'Có lỗi đồng bộ dữ liệu, vui lòng tải lại trang!.',
                                    error
                                )
                            );
                        case 500: // Internal Server Error
                            this.showUserLogMessage(
                                'A server error occurred. Please try again later or contact support.',
                                'error'
                            );
                            return Promise.reject(
                                new ClassroomCriticalError(
                                    { type: 'UNCLASSIFIED', data: true },
                                    'Lỗi hệ thống. Vui lòng tải lại trang.',
                                    error
                                )
                            );
                        case 502: // Bad Gateway
                        case 503: // Service Unavailable
                        case 504: // Gateway Timeout
                            this.showUserLogMessage(
                                'A server error occurred. Please try again later or contact support.',
                                'error'
                            );
                            return Promise.reject(
                                new ClassroomNonCriticalError(
                                    { type: 'CONNECTION_ERROR', data: true },
                                    'Lỗi hệ thống. Vui lòng tải lại trang.',
                                    error
                                )
                            );
                        default:
                            console.error(`Unhandled HTTP Error: ${status} for URL: ${requestUrl}`);
                            this.showUserLogMessage(`An unexpected error occurred (Status: ${status}).`, 'error');
                            return Promise.reject(
                                new ClassroomNonCriticalError(
                                    { type: 'UNCLASSIFIED', data: true },
                                    'Lỗi không xác định, vui lòng tải lại trang!.',
                                    error
                                )
                            );
                    }
                } else if (error.request) {
                    // --- Request was made but no response received ---
                    // This usually indicates a network issue (offline, DNS, CORS preflight failure, timeout)
                    this.showUserLogMessage(
                        'Network error. Please check your internet connection and try again.',
                        'error'
                    );
                    // Reject with a specific ConnectionError
                    return Promise.reject(
                        new ClassroomNonCriticalError(
                            { type: 'CONNECTION_ERROR', data: true },
                            'Lỗi kết nối. Vui lòng tải lại trang',
                            error
                        )
                    );
                } else {
                    // --- Error occurred setting up the request ---
                    this.showUserLogMessage('An error occurred while preparing the request.', 'error');
                    // This is likely a client-side issue, but treat as ConnectionError for simplicity?
                    return Promise.reject(
                        new ClassroomNonCriticalError(
                            { type: 'CONNECTION_ERROR', data: true },
                            'Lỗi kết nối. Vui lòng tải lại trang',
                            error
                        )
                    ); // Or a different configuration error type
                }

                // IMPORTANT: Reject the promise to propagate the error
                // If specific errors were handled above by rejecting with custom types, this won't be reached for those cases.
                return Promise.reject(error);
            }
        );
    }

    /**
     * Helper method to log messages (simulating showing user messages).
     * Replace with actual UI notification logic if needed.
     * @param message - The message to display.
     * @param type - The type of message (e.g., 'error', 'warning').
     */
    private showUserLogMessage(message: string, type: 'success' | 'error' | 'warning' | 'info') {
        console.log(`[${type.toUpperCase()}] User Message: ${message}`);
    }

    // --- API Methods ---

    /**
     * Registers a peer for the given user in a specific room.
     * @param userId - The ID of the user.
     * @param roomId - The ID of the room.
     * @returns A promise resolving to the assigned peerId string, or rejects on error.
     */
    async registerPeer(userId: string, roomId: string): Promise<string> {
        const resp = await this.api.get<{ peerId: string }>(`/manage/registerUserPeer`, {
            params: { userId, roomId },
        });
        return resp.data.peerId; // Directly return the peerId from the typed response data
    }

    /**
     * Sends a request to notify the backend that the user (peer) is disconnecting/offline.
     * This is often called during window unload events.
     * @param peerId - The ID of the peer going offline.
     * @returns A promise that resolves when the request is sent (doesn't guarantee backend processing).
     */
    async notifyUserOffline(peerId: string): Promise<void> {
        // Use POST or a more appropriate verb if the backend expects it for state changes. GET might be cached.
        // Consider using navigator.sendBeacon for more reliability during unload.
        try {
            await this.api.post(`/signal/disconnect/${peerId}`); // Changed to POST
            console.log(`Sent offline notification for peer ${peerId}`);
        } catch (error) {
            // We might swallow the error here as it's best-effort during unload.
            console.warn(`Failed to send offline notification for peer ${peerId}:`, error.message);
        }
    }

    /**
     * Polls the backend for incoming signal messages for the specified peer.
     * This is a long-polling request.
     * @param peerId - The ID of the peer to poll for.
     * @returns A promise resolving to the received SignalMessage, null if no message, or rejects on error.
     */
    async pollSignal(peerId: string): Promise<SignalMessage | null> {
        const resp = await this.api.get<SignalMessage | null>(`/signal/poll/${peerId}`);
        return resp.data;
    }

    /**
     * Joins the specified classroom room.
     * @param roomId - The ID of the room to join.
     * @returns A promise resolving to the syncPeer ID (likely the remote peer's ID), or rejects on error.
     */
    async joinRoom(roomId: string): Promise<string> {
        const resp = await this.api.get<string>(`/signal/join/${roomId}`);
        return resp.data;
    }

    /**
     * Sends a signal message (e.g., offer, answer, candidate) to the backend for routing.
     * @param msg - The SignalMessage object to send.
     * @returns A promise resolving to true if the backend accepted the message (e.g., status 200), false otherwise, or rejects on error.
     */
    async sendSignal(msg: SignalMessage): Promise<any> {
        const resp = await this.api.post('/signal/send', msg, {
            headers: { 'Content-Type': 'application/json' },
        });
        return resp;
    }

    /**
     * Sends a reply to a specific request signal message.
     * @param replyMsg - The SignalMessage containing the reply data and original requestId.
     * @returns A promise resolving with the AxiosResponse, or rejects on error.
     */
    async reply(replyMsg: SignalMessage): Promise<AxiosResponse> {
        return this.api.post('/signal/reply', replyMsg, {
            headers: { 'Content-Type': 'application/json' },
        });
    }

    /**
     * Fetches detailed information about the specified classroom room.
     * @param roomId - The ID of the room.
     * @returns A promise resolving to the ClassroomInfo object.
     */
    async fetchRoomInfo(roomId: string): Promise<ClassroomInfo> {
        const resp = await this.api.get<ClassroomInfo>(`/classroom/roominfo/${roomId}`);
        return resp.data;
    }

    /**
     * Fetches the state data for a single coordinator state (viewport).
     * Transforms the raw response data (maps stored as objects) into the expected format.
     * @param coordId - The ID of the coordinator state.
     * @returns A promise resolving to the ClassRoomCoordinatorState object.
     */
    async fetchCoordinatorState(coordId: ViewportId): Promise<ClassRoomCoordinatorState> {
        const resp = await this.api.get<any>(`/coordinatorstate/${coordId}`); // Use any for raw data
        const rawState = resp.data;

        // Transform raw data into the expected ClassRoomCoordinatorState structure
        const coordState: ClassRoomCoordinatorState = {
            ...rawState, // Spread other properties
            // Convert object representations back to Maps
            docMapping: new Map(Object.entries(rawState.docMapping || {})),
            layers: new Map(Object.entries(rawState.layers || {})),
            // Ensure docSettings is an object (it might be null/undefined)
            docSettings: rawState.docSettings || {},
        };

        // Backward compatibility or data correction: map zindex to index if needed
        coordState.layers.forEach(layerInfo => {
            if (layerInfo.zindex !== undefined && layerInfo.index === undefined) {
                layerInfo.index = layerInfo.zindex;
            }
        });

        return coordState;
    }

    /**
     * Fetches state data for multiple coordinator states based on their IDs.
     * Transforms the raw response data for each state.
     * @param coordStateIds - An array of coordinator state IDs.
     * @returns A promise resolving to an array of ClassRoomCoordinatorState objects.
     */
    async fetchCoordinatorStates(coordStateIds: ViewportId[]): Promise<Array<ClassRoomCoordinatorState>> {
        if (!coordStateIds || coordStateIds.length === 0) {
            return []; // Return empty array if no IDs provided
        }
        const resp = await this.api.post<any[]>(`/coordinatorstate/ids`, { coordStateIds }); // Send IDs in body
        const rawStates = resp.data;

        // Transform each raw state object
        return rawStates.map(rawState => {
            const coordState: ClassRoomCoordinatorState = {
                ...rawState,
                docMapping: new Map(Object.entries(rawState.docMapping || {})),
                layers: new Map(Object.entries(rawState.layers || {})),
                docSettings: rawState.docSettings || {},
            };
            coordState.layers.forEach(layerInfo => {
                if (layerInfo.zindex !== undefined && layerInfo.index === undefined) {
                    layerInfo.index = layerInfo.zindex;
                }
            });
            return coordState;
        });
    }

    /**
     * Fetches all coordinator states owned by a specific user within a given room.
     * Transforms the raw response data for each state.
     * @param owner - The user ID of the owner.
     * @param roomId - The ID of the room.
     * @returns A promise resolving to an array of ClassRoomCoordinatorState objects.
     */
    async fetchCoordinatorStateByUser(owner: string, roomId: string): Promise<Array<ClassRoomCoordinatorState>> {
        const resp = await this.api.get<any[]>(`/coordinatorstate/fetchByUser`, {
            params: { roomId, ownerId: owner }, // Use correct param names expected by backend
        });
        const rawStates = resp.data;

        // Transform each raw state object
        return rawStates.map(rawState => {
            const coordState: ClassRoomCoordinatorState = {
                ...rawState,
                docMapping: new Map(Object.entries(rawState.docMapping || {})),
                layers: new Map(Object.entries(rawState.layers || {})),
                docSettings: rawState.docSettings || {},
            };
            coordState.layers.forEach(layerInfo => {
                if (layerInfo.zindex !== undefined && layerInfo.index === undefined) {
                    layerInfo.index = layerInfo.zindex;
                }
            });
            return coordState;
        });
    }

    /**
     * Fetches document content/data using its global ID.
     * @param channelCode - The editor channel code.
     * @param globalId - The global ID of the document.
     * @param responseType - The expected response type (e.g., 'json', 'arraybuffer').
     * @returns A promise resolving to the fetched document data.
     */
    async fetchDocumentByGlobalId(channelCode: number, globalId: DocumentId, responseType: ResponseType): Promise<any> {
        const resp = await this.api.get<any>(`/document/fetchByGlobalId`, {
            params: { channelCode, globalId },
            responseType, // Set expected response type
        });
        return resp.data;
    }

    /**
     * Fetches document content/data using its local ID within a specific coordinator state.
     * @param coordStateId - The ID of the coordinator state context.
     * @param channelCode - The editor channel code.
     * @param localId - The local ID of the document.
     * @param responseType - The expected response type.
     * @returns A promise resolving to the fetched document data.
     */
    async fetchDocumentByLocalId(
        coordStateId: ViewportId,
        channelCode: number,
        localId: DocLocalId,
        responseType: ResponseType
    ): Promise<any> {
        const resp = await this.api.get<any>(`/document/fetchByLocalId`, {
            params: { coordStateId, channelCode, localId },
            responseType,
        });
        return resp.data;
    }

    /**
     * Creates a new coordinator state on the backend.
     * @param peerId - The peer ID of the user creating the state.
     * @param title - Optional title for the new state.
     * @returns A promise resolving to the ID of the newly created coordinator state.
     */
    async createCoordinatorState(peerId: string, title?: string): Promise<string> {
        const resp = await this.api.post<string>(`/coordinatorstate/create`, { peerId, title });
        return resp.data;
    }

    /**
     * Deletes a coordinator state on the backend.
     * Requires the peer ID and the expected current version for optimistic locking.
     * @param peerId - The peer ID of the user deleting the state.
     * @param coordStateId - The ID of the state to delete.
     * @param coordStateVersion - The expected current version of the state.
     * @returns A promise resolving to the backend response data (structure might vary).
     */
    async deleteCoordinatorState(peerId: string, coordStateId: ViewportId, coordStateVersion: number): Promise<string> {
        // Backend might return the ID or just a success status
        const resp = await this.api.post<string>(`/coordinatorstate/delete`, {
            peerId,
            coordStateId,
            coordStateVersion,
        });
        return resp.data; // Return whatever the backend sends on success
    }

    /**
     * Duplicates an existing coordinator state on the backend.
     * @param peerId - The peer ID of the user duplicating the state.
     * @param coordStateId - The ID of the state to duplicate.
     * @returns A promise resolving to the ID of the newly created duplicated state.
     */
    async duplicateCoordinatorState(peerId: string, coordStateId: ViewportId): Promise<string> {
        const resp = await this.api.post<string>(`/coordinatorstate/duplicate`, {
            peerId,
            coordStateId,
        });
        return resp.data;
    }

    /**
     * Renames a coordinator state on the backend.
     * Requires the peer ID and the expected current version for optimistic locking.
     * @param peerId - The peer ID of the user renaming the state.
     * @param coordStateId - The ID of the state to rename.
     * @param title - The new title.
     * @param coordStateVersion - The expected current version of the state.
     * @returns A promise resolving to the backend response data (e.g., the ID or success status).
     */
    async renameCoordinatorState(peerId: string, coordStateId: ViewportId, title: string): Promise<string> {
        // Return type might vary
        const resp = await this.api.post<string>(`/coordinatorstate/rename`, {
            coordStateId,
            peerId,
            title,
        });
        return resp.data; // Return backend response
    }

    /**
     * Marks a specific coordinator state as the one being currently presented in the classroom.
     * @param peerId - The peer ID of the user initiating the presentation (usually the presenter).
     * @param coordStateId - The ID of the state to present.
     * @returns A promise resolving to the backend response data.
     */
    async presentCoordState(peerId: string, coordStateId: ViewportId): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/presentCoordState`, { peerId, coordStateId });
        return resp.data;
    }

    async requestPinBoard(peerId: string, coordStateId: ViewportId): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/request-pin-tab`, { peerId, coordStateId });
        return resp.data;
    }

    async cancelRequestPinBoard(peerId: string, coordStateId: ViewportId): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/cancel-request-pin-tab`, { peerId, coordStateId });
        return resp.data;
    }

    async rejectRequestPinBoard(peerId: string, targetUserId: string, coordStateId: ViewportId): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/reject-request-pin-tab`, {
            peerId,
            targetUserId,
            coordStateId,
        });
        return resp.data;
    }

    async approveRequestPinBoard(peerId: string, targetUserId: string, coordStateId: ViewportId): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/approve-request-pin-tab`, {
            peerId,
            targetUserId,
            coordStateId,
        });
        return resp.data;
    }

    /**
     * Pins a coordinator state, making it easily accessible (e.g., in a tab bar).
     * Requires the peer ID and the expected current version for optimistic locking.
     * @param peerId - The peer ID of the user pinning the state (usually the room owner).
     * @param coordStateId - The ID of the state to pin.
     * @param coordStateVersion - The expected current version of the state.
     * @returns A promise resolving to the backend response data.
     */
    async pinCoordinatorState(peerId: string, coordStateId: ViewportId, coordStateVersion: number): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/pin`, {
            peerId,
            coordStateId,
            coordStateVersion,
        });
        return resp.data;
    }

    /**
     * Unpins a coordinator state.
     * Requires the peer ID and the expected current version for optimistic locking.
     * @param peerId - The peer ID of the user unpinning the state (usually the room owner).
     * @param coordStateId - The ID of the state to unpin.
     * @param coordStateVersion - The expected current version of the state.
     * @returns A promise resolving to the backend response data.
     */
    async unpinCoordinatorState(peerId: string, coordStateId: ViewportId, coordStateVersion: number): Promise<any> {
        const resp = await this.api.post<any>(`/coordinatorstate/unpin`, { peerId, coordStateId, coordStateVersion });
        return resp.data;
    }

    /**
     * Sends a request to the backend to add or update document mappings and layer information
     * within a coordinator state. Used for creating new documents/layers or updating existing ones.
     * Requires the expected current version for optimistic locking.
     * @param request - The request object containing changes and version information.
     * @returns A promise resolving to the backend response data.
     */
    async addMultipleDocMappingAndLayers(request: DocsMappingAndLayersRequest): Promise<any> {
        const resp = await this.api.post('/coordinatorstate/add-docs-mapping-and-layers', request, {
            headers: { 'Content-Type': 'application/json' },
        });
        return resp.data;
    }

    /**
     * Sends a request to the backend to remove document mappings and/or layers
     * within a coordinator state.
     * Requires the expected current version for optimistic locking.
     * @param request - The request object containing removals and version information.
     * @returns A promise resolving to the backend response data.
     */
    async removeMultipleDocMappingAndLayers(request: DocsMappingAndLayersRequest): Promise<any> {
        // Uses same request type? Check backend.
        const resp = await this.api.post('/coordinatorstate/remove-multiple-doc-mapping-and-layers', request, {
            headers: { 'Content-Type': 'application/json' },
        });
        return resp.data;
    }

    /**
     * Fetches the local content state (all local documents) for a given coordinator ID.
     * Parses the JSON content stored in the backend.
     * @param coordId - The ID of the coordinator state.
     * @returns A promise resolving to the ClassroomLocalContentState object.
     */
    async fetchLocalContentState(coordId: string): Promise<ClassroomLocalContentState> {
        const resp = await this.api.get<any>(`/localcontent/${coordId}`); // Use any for raw data
        const contentStateRaw = resp.data;
        const localContents: Map<string, DocLocalContent> = new Map();

        // Check if the raw state and contentMapping exist
        if (contentStateRaw && contentStateRaw.contentMapping) {
            for (const docKey of Object.keys(contentStateRaw.contentMapping)) {
                const contentObj = contentStateRaw.contentMapping[docKey];
                // Check if the content object and the actual content string exist
                if (contentObj && contentObj.content != null) {
                    // Check for null or undefined content string
                    try {
                        // Parse the JSON string stored in the database
                        const parsedContent = JSON.parse(contentObj.content);
                        localContents.set(docKey, {
                            version: contentObj.version,
                            content: parsedContent,
                        });
                    } catch (parseError) {
                        console.error(
                            `Error parsing local content JSON for key ${docKey}:`,
                            parseError,
                            'Raw content:',
                            contentObj.content
                        );
                        // Decide how to handle parse errors: skip, set to null, throw?
                        // Skipping for now to avoid breaking the entire load.
                        // throw parseError; // Or re-throw if parsing failure is critical
                    }
                } else {
                    console.warn(`Missing content data for local document key ${docKey}.`);
                }
            }
        } else {
            console.warn(`No local content mapping found for coordinator state ${coordId}.`);
        }

        return { localContents }; // Return the map, potentially empty
    }

    /**
     * Sends updates for local content documents to the backend.
     * @param request - The request object containing the changes.
     * @returns A promise resolving to the backend response data (likely success status).
     */
    async updateLocalContentState(request: UpdateLocalContentRequest): Promise<any> {
        // Return type likely void or simple success
        const resp = await this.api.post<any>('/localcontent/update', request, {
            headers: { 'Content-Type': 'application/json' },
        });
        return resp.data;
    }
}

// --- Interface Definitions ---

/** Represents changes to a single layer's mapping information. */
export interface LayerMappingChange {
    layerId: number;
    layerIndex: number;
    positionStart?: Position; // Optional start position
    positionEnd?: Position; // Optional end position
}

/** Represents changes to a single document's mapping and its layers. */
export interface DocumentMappingChange {
    channelCode: number;
    docLocalId: DocLocalId;
    docGlobalId: DocumentId;
    layerUpdates?: LayerMappingChange[]; // Layers being updated
    layerRemoves?: LayerMappingChange[]; // Layers being removed
    layerAdds?: LayerMappingChange[]; // Layers being added
}

/** Request structure for adding/updating multiple document mappings and layers. */
export interface DocsMappingAndLayersRequest {
    peerId: string;
    coordStateId: ViewportId;
    coordStateVersion: number; // Expected version for optimistic locking
    documents: DocumentMappingChange[];
    defaultSetting?: DefaultSetting; // Optional: Include if updating settings
    presenterState?: PresenterState; // Optional: Include if updating presenter state
}

/** Request structure for updating local content for multiple documents. */
export interface UpdateLocalContentRequest {
    coordStateId: string;
    changes: {
        channelCode: number;
        docLocalId: number;
        version: number; // Version of the local content being saved
        content: string; // The actual content, JSON stringified
    }[];
}
