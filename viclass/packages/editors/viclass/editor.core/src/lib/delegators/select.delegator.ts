import { BehaviorSubject } from 'rxjs';
import {
    <PERSON>B<PERSON>,
    MouseEventData,
    VDocCtrl,
    VDocLayerCtrl,
    VEventData,
    VEventListener,
    ViewportDisableCES,
    ViewportId,
} from '../api';
import { DefaultEventEmitter } from '../default.event.source';
import { DefaultVDocCtrl } from '../default.vdoc.ctrl';
import { SelectContext, SelectHitContext, SupportSelectFeature } from '../tools';
import { DocEventState, HasSelectionFeature } from './model';

export type SupportSelectEditor<T extends VDocCtrl> = EditorBase<T> & HasSelectionFeature;

export type SelectEventHandlers<TDocCtrl extends VDocCtrl> = {
    onSelect?: (docCtrl: TDocCtrl) => void | Promise<void>;
    onDeselect?: (docCtrl: TDocCtrl) => void | Promise<void>;
};

export type FocusDocEventType = 'doc-focused' | 'doc-unfocused';

export interface DocFocusedES<T extends VDocCtrl> extends DocEventState {
    docCtrl: T;
}

/**
 * Escape to an outer context.
 * Ex: using arrow keys in math subviewport to escape to the parent word doc context
 */
export type EscapeDocEventType = 'doc-escaped';

export type EscapeDirection = 'forward' | 'backward' | 'upward' | 'downward';

export interface DocEscapeES<T extends VDocCtrl> extends DocEventState {
    docCtrl: T;
    direction?: EscapeDirection;
}

export type FocusDocEvent<T extends VDocCtrl> =
    | VEventData<FocusDocEventType, SupportSelectEditor<T>, DocFocusedES<T>>
    | VEventData<EscapeDocEventType, SupportSelectEditor<T>, DocEscapeES<T>>;

export class SelectDelegator<TDocCtrl extends VDocCtrl> implements SupportSelectFeature {
    private readonly focusedDocsCtrls$: BehaviorSubject<Map<ViewportId, TDocCtrl[]>> = new BehaviorSubject(new Map());

    private _eventEmitter = new DefaultEventEmitter<FocusDocEvent<TDocCtrl>>();

    constructor(
        private editor: SupportSelectEditor<TDocCtrl>,
        private handlers: SelectEventHandlers<TDocCtrl> = {}
    ) {}

    registerDocEventListener(listener: VEventListener<FocusDocEvent<TDocCtrl>>) {
        this._eventEmitter.registerListener(listener);
    }

    unregisterDocEventListener(listener: VEventListener<FocusDocEvent<TDocCtrl>>) {
        this._eventEmitter.unregisterListener(listener);
    }

    checkHit(layer: VDocLayerCtrl, event: MouseEventData<any>, multiple?: boolean): SelectHitContext | undefined {
        if (!event || !layer.doc || layer.doc.editor.editorType !== this.editor.editorType) return undefined;

        return (layer.doc as DefaultVDocCtrl).checkHit(event, layer);
    }

    select(hitCtx: SelectHitContext, multiple?: boolean): SelectContext {
        const context = this.doSelectDoc(hitCtx.doc as TDocCtrl, false);
        context.selectDetails = hitCtx.hitDetails;
        return context;
    }

    selectDoc(doc: TDocCtrl, multiple?: boolean): SelectContext {
        return this.doSelectDoc(doc, multiple);
    }

    deselect(selectCtx: SelectContext) {
        return this.doDeselectDoc(selectCtx.doc as TDocCtrl, false);
    }

    deselectDoc(doc: TDocCtrl) {
        return this.doDeselectDoc(doc);
    }

    doSelectDoc(doc: TDocCtrl, multiple?: boolean, informSelectionFeature = true): SelectContext {
        if (doc.editor.editorType === this.editor.editorType) {
            const viewPortId = doc.viewport.id;
            this.doFocusDocCtrl(doc);
            const context: SelectContext = {
                doc: doc,
                supporter: this,
                selectDetails: undefined,
            };
            if (informSelectionFeature) this.editor.selectionFeature.onSelect(context);

            if (this.handlers.onSelect) this.handlers.onSelect(doc);
            return context;
        }

        throw new Error("Math editor couldn't select document of different type");
    }

    doDeselectDoc(doc: TDocCtrl, informSelectionFeature = true): SelectContext {
        if (doc.editor.editorType === this.editor.editorType) {
            this.doBlurDocCtrl(doc);
            const context: SelectContext = {
                doc: doc,
                supporter: this,
                selectDetails: {},
            };

            if (informSelectionFeature) this.editor.selectionFeature.onDeselect(context);
            if (this.handlers.onDeselect) this.handlers.onDeselect(doc);

            return context;
        }

        throw new Error("Geo Editor couldn't select document of different type");
    }

    getFocusedDocs(viewportId: ViewportId): TDocCtrl[] {
        return this.focusedDocsCtrls$.value.get(viewportId) || [];
    }

    unfocusCurDocCtrls(viewportId: ViewportId): TDocCtrl[] | undefined {
        // remove focus on the currently focused math doc if any for the viewport
        if (this.focusedDocsCtrls$.value.has(viewportId)) {
            const curSelectingDocs = this.focusedDocsCtrls$.value.get(viewportId) || [];
            this.blurDocCtrls(curSelectingDocs);
            return curSelectingDocs;
        }

        return undefined;
    }

    async blurDocCtrls(docCtrls: TDocCtrl[]): Promise<void> {
        await Promise.all(docCtrls.map(docCtrl => this.doBlurDocCtrl(docCtrl)));
    }

    async doBlurDocCtrl(docCtrl: TDocCtrl): Promise<void> {
        if (!this.focusedDocsCtrls$.value.has(docCtrl.viewport.id)) return;

        const focusedDocsCtrl = this.getFocusedDocs(docCtrl.viewport.id);
        const docIndex = focusedDocsCtrl.findIndex(d => d == docCtrl);

        if (docIndex >= 0) {
            focusedDocsCtrl.splice(docIndex, 1);

            const focusedDocsCtrls = this.focusedDocsCtrls$.value;

            if (!focusedDocsCtrl.length) focusedDocsCtrls.delete(docCtrl.viewport.id);
            else focusedDocsCtrls.set(docCtrl.viewport.id, focusedDocsCtrl);

            this.focusedDocsCtrls$.next(focusedDocsCtrls);

            await this._eventEmitter.emit({
                eventType: 'doc-unfocused',
                source: this.editor,
                state: {
                    vm: docCtrl.viewport,
                    docCtrl: docCtrl,
                } as DocFocusedES<TDocCtrl>,
            });
        }
    }

    async focusDocCtrl(docCtrl: TDocCtrl): Promise<void> {
        await this.doFocusDocCtrl(docCtrl);
        this.editor.selectionFeature.onSelect({
            doc: docCtrl,
            supporter: this,
            selectDetails: undefined,
        });
    }

    async doFocusDocCtrl(docCtrl: TDocCtrl): Promise<void> {
        const viewportId = docCtrl.viewport.id;

        const focusedDocsCtrls = this.focusedDocsCtrls$.value;
        if (focusedDocsCtrls.has(viewportId)) {
            const currDocsCtrl = this.getFocusedDocs(viewportId);
            if (!currDocsCtrl.find(doc => doc.state.id === docCtrl.state.id)) {
                currDocsCtrl.push(docCtrl);
                focusedDocsCtrls.set(viewportId, currDocsCtrl);
                this.focusedDocsCtrls$.next(focusedDocsCtrls);
            }
        } else {
            focusedDocsCtrls.set(viewportId, [docCtrl]);
            this.focusedDocsCtrls$.next(focusedDocsCtrls);
        }

        await this._eventEmitter.emit({
            eventType: 'doc-focused',
            source: this.editor,
            state: {
                vm: docCtrl.viewport,
                docCtrl: docCtrl,
            } as DocFocusedES<TDocCtrl>,
        });
    }

    onViewportRemoved(viewportId: ViewportId) {
        this.unfocusCurDocCtrls(viewportId);

        const reg = this.editor.regDelegator.deleteDocReg(viewportId);

        if (reg == null) return; // viewport not previously used inside this editor, nothing to do with it, so return

        const docCtrls = reg.allEntities();

        for (const ctrl of docCtrls) {
            // for all docs on viewport
            reg.removeEntity(ctrl.state.id);
            const layerReg = this.editor.regDelegator.deleteLayerReg(viewportId, ctrl.state.id)!;

            const renders = layerReg.allEntities();
            for (const r of renders) {
                layerReg.removeEntity(r.layerState.id);
            }
        }
    }

    onViewportDisabled(viewportId: ViewportId, evs: ViewportDisableCES) {
        if (evs.cleanSelection) {
            const focusedDocs = this.getFocusedDocs(viewportId);
            if (focusedDocs.length > 0) this.blurDocCtrls(focusedDocs);
        }
    }

    async escape(docCtrl: TDocCtrl, direction?: EscapeDirection) {
        await this._eventEmitter.emit({
            eventType: 'doc-escaped',
            source: this.editor,
            state: {
                vm: docCtrl.viewport,
                docCtrl: docCtrl,
                direction,
            } as DocFocusedES<TDocCtrl>,
        });
    }
}
