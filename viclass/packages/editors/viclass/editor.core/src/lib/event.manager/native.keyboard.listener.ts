import { KeyboardEventData, KeyboardEventListener, KeyboardHandlingItem, NativeEventTarget } from '../api';
import { DefaultViewportEventManager, WrappedEventListener } from './default.event.manager';
import { keyboardHandlingKey } from '../util';

/**
 *
 * <AUTHOR>
 */
export class NativeKeyboardListener implements KeyboardEventListener<NativeEventTarget<any>> {
    private readonly _globalKeyboardListener: GlobalKeyboardListener = new GlobalKeyboardListener();

    readonly keyboardHandling: Map<string, KeyboardEventListener<NativeEventTarget<any>>[]> = new Map();
    private readonly _captureAlKeyboardElements: Map<string, KeyboardEventListener<NativeEventTarget<any>>> = new Map();

    constructor(private _p: DefaultViewportEventManager) {}

    onEvent(
        eventData: KeyboardEventData<NativeEventTarget<any>>
    ): KeyboardEventData<NativeEventTarget<any>> | Promise<KeyboardEventData<NativeEventTarget<any>>> {
        if (!this._p.isEnable()) return eventData;

        if (!eventData.continue) return eventData;
        // if (eventData.nativeEvent.defaultPrevented) return eventData

        // only handle capture all element for focusing element
        if (eventData.nativeEvent.target == document.activeElement) {
            const captureAllHandler = this.captureAllKeyboardEventHandler(eventData.nativeEvent.target as Element);
            captureAllHandler?.onEvent(eventData);
        }

        if (eventData.continue) {
            const key = keyboardHandlingKey(eventData.eventType, eventData.getKeys);

            const handlers = this.keyboardHandling.get(key) ?? [];
            for (let i = handlers.length - 1; i >= 0; i--) {
                const handler = handlers[i];
                if (eventData.continue) handler?.onEvent(eventData);
                else break;
            }

            if (eventData.continue) this._globalKeyboardListener.onEvent(eventData);
        }

        if (!eventData.continue) eventData.nativeEvent.stopPropagation(); // if not continuing, then disable default action

        return eventData;
    }

    resetKeyboardHandling() {
        this.keyboardHandling.clear();
        this._globalKeyboardListener.resetKeyboardHandling();
    }

    registerKeyboardHandling(
        handlingItem: KeyboardHandlingItem,
        handler: KeyboardEventListener<NativeEventTarget<any>>
    ) {
        const wrapped: any = new WrappedEventListener(handler);
        if (!handlingItem.global) {
            const key = keyboardHandlingKey(handlingItem.event, handlingItem.keys);
            const handlers = this.keyboardHandling.get(key) ?? [];
            this.keyboardHandling.set(key, handlers.concat(wrapped));
        } else {
            this._globalKeyboardListener.registerKeyboardHandling(handlingItem, wrapped);
        }
    }

    captureAllKeyboardEvent<Source extends NativeEventTarget<Source>>(
        el: Element,
        handler: KeyboardEventListener<NativeEventTarget<Source>>
    ) {
        const wrapped: any = new WrappedEventListener(handler);
        this._captureAlKeyboardElements.set(el.id, wrapped);
    }

    captureAllKeyboardEventHandler<Source extends NativeEventTarget<Source>>(
        el: Element
    ): KeyboardEventListener<NativeEventTarget<Source>> | undefined {
        return this._captureAlKeyboardElements.get(el.id);
    }

    unCaptureAllKeyboardEvent(el: Element) {
        this._captureAlKeyboardElements.delete(el.id);
    }
}

/**
 *
 * <AUTHOR>
 */
export class GlobalKeyboardListener implements KeyboardEventListener<NativeEventTarget<any>> {
    private keyboardHandling: Map<string, KeyboardEventListener<NativeEventTarget<any>>[]> = new Map();

    constructor() {}

    async onEvent(
        eventData: KeyboardEventData<NativeEventTarget<any>>
    ): Promise<KeyboardEventData<NativeEventTarget<any>>> {
        const handlers = this.keyboardHandling.get(keyboardHandlingKey(eventData.eventType, eventData.getKeys)) ?? [];
        eventData.continue = true;
        for (let i = handlers.length - 1; i >= 0; i--) {
            const handler = handlers[i];
            if (eventData.continue) eventData = await handler.onEvent(eventData);
            else break;
        }
        return eventData;
    }

    registerKeyboardHandling(
        handlingItem: KeyboardHandlingItem,
        handler: KeyboardEventListener<NativeEventTarget<any>>
    ) {
        if (handlingItem.global) {
            const handlers =
                this.keyboardHandling.get(keyboardHandlingKey(handlingItem.event, handlingItem.keys)) ?? [];
            this.keyboardHandling.set(
                keyboardHandlingKey(handlingItem.event, handlingItem.keys),
                handlers.concat(handler)
            );
        }
    }

    resetKeyboardHandling() {
        this.keyboardHandling.clear();
    }
}
