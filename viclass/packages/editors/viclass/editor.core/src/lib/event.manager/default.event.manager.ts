import {
    <PERSON>E<PERSON>,
    CoordinatorEventState,
    <PERSON><PERSON>ultKeyboardEventData,
    DocumentEditor,
    EditorCoordinator,
    EditorEventManager,
    EditorFocusEventData,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    PointerEventListener,
    PointerHandlingItem,
    VDocLayerCtrl,
    VEventData,
    VEventListener,
    ViewportManager,
} from '../api';
import { DefaultEventEmitter } from '../default.event.source';
import { LayerEventListener } from './layer.event.listener';
import { NativeKeyboardListener } from './native.keyboard.listener';
import { NativeMouseListener } from './native.mouse.listener';
import { NativePointerListener } from './native.pointer.listener';

/**
 * Source encapsulates elements that should be monitored for keyboard events by the event managers
 */
class ElementKeyboardSource implements NativeEventTarget<any> {
    // when someone is attaching to the keyboard emitter of this source or not
    private isCurrentlyAttached = false;
    private nativeHandler = (e: Event) => {
        this.onElementNativeKeyEvent(e as KeyboardEvent);
    };

    nativeKeyboardEventEmitter: DefaultEventEmitter<KeyboardEventData<any>> = new DefaultEventEmitter();

    monitoredEls: (HTMLElement | SVGElement)[] = [];

    constructor(private em: DefaultViewportEventManager) {
        this.monitoredEls = ([] as (HTMLElement | SVGElement)[]).concat(em.vms.map(vm => vm.rootEl)); // the root element of the viewport is always monitored

        this.nativeKeyboardEventEmitter.onBeforeListenerAdded = listener => {
            for (const el of this.monitoredEls) {
                this.setListenerForEl(el);
            }
            this.isCurrentlyAttached = true;
        };

        this.nativeKeyboardEventEmitter.onAfterListenerRemoved = listener => {
            for (const el of this.monitoredEls) {
                this.removeListenerForEl(el);
            }
            this.isCurrentlyAttached = false;
        };
    }

    /**
     * For external element, we only monitor keyboard, becos clicking mouse outside of viewport is unlikely
     * an event we care
     * @param event
     */
    // event is of type JQuery.Event
    onElementNativeKeyEvent(event: KeyboardEvent) {
        if (this.em.focusedVM.length == 0) return;
        const lastFocusedVM = this.em.focusedVM[this.em.focusedVM.length - 1];
        const data = new DefaultKeyboardEventData<ElementKeyboardSource>(lastFocusedVM, this, event);
        this.nativeKeyboardEventEmitter.emitSync(data);
    }

    captureKeyboardEventFor(els: (HTMLElement | SVGElement)[]) {
        // filtering so that there is no duplicate
        for (const e of els) {
            this.monitoredEls = this.monitoredEls.filter(x => !els.includes(x)).concat(els);
            if (this.isCurrentlyAttached) this.setListenerForEl(e);
        }
    }

    uncaptureKeyboardEventFor(els: Element[]) {
        for (const e of els) {
            this.monitoredEls = this.monitoredEls.filter(x => !els.includes(x));
            if (this.isCurrentlyAttached) this.removeListenerForEl(e);
        }
    }

    private setListenerForEl(el: HTMLElement | SVGElement) {
        el.removeEventListener('keyup', this.nativeHandler);
        el.removeEventListener('keydown', this.nativeHandler);
        el.addEventListener('keyup', this.nativeHandler);
        el.addEventListener('keydown', this.nativeHandler);
    }

    private removeListenerForEl(el) {
        el.removeEventListener('keyup', this.nativeHandler);
        el.removeEventListener('keydown', this.nativeHandler);
    }
}

export class DefaultViewportEventManager implements EditorEventManager {
    private readonly nativeMouseListener = new NativeMouseListener(this);
    private readonly nativeKeyboardListener = new NativeKeyboardListener(this);
    private readonly nativePointerListener = new NativePointerListener(this);
    private readonly layerEventListener: LayerEventListener = new LayerEventListener(
        this.nativeMouseListener,
        this.nativeKeyboardListener,
        this.nativePointerListener
    );

    private _enabled = false;

    focusedVM: ViewportManager[] = [];
    vms: ViewportManager[] = [];

    private extElSrc = new ElementKeyboardSource(this);

    constructor(public coordinator: EditorCoordinator) {
        coordinator.registerCoordEventListener(this.coordListener);
        this.enable();
    }

    coordListener = new (class implements VEventListener<CoordinatorEvent> {
        constructor(public em: DefaultViewportEventManager) {}

        onEvent(eventData: CoordinatorEvent): CoordinatorEvent {
            switch (eventData.eventType) {
                case 'viewport-focusin': {
                    const vm = this.em.coordinator.getViewportManager((eventData.state as CoordinatorEventState).vmId);
                    this.em.focusedVM.push(vm);
                    break;
                }
                case 'viewport-focusout': {
                    const id = (eventData.state as CoordinatorEventState).vmId;
                    this.em.focusedVM = this.em.focusedVM.filter((vm: ViewportManager) => vm.id != id);
                    break;
                }
            }

            return eventData;
        }
    })(this);

    registerMouseHandlingOnLayer<L extends VDocLayerCtrl>(
        handlingItems: MouseHandlingItem[],
        handler: MouseEventListener<L>,
        layer: L
    ) {
        this.nativeMouseListener.registerMouseHandlingOnLayer(handlingItems, handler, layer);
    }

    clearMouseHandlingOnLayer(layer: VDocLayerCtrl) {
        this.nativeMouseListener.clearMouseHandlingOnLayer(layer);
    }

    registerPointerHandlingOnLayer<L extends VDocLayerCtrl>(
        handlingItems: PointerHandlingItem[],
        handler: PointerEventListener<L>,
        layer: L
    ) {
        this.nativePointerListener.registerPointerHandlingOnLayer(handlingItems, handler, layer);
    }

    clearPointerHandlingOnLayer(layer: VDocLayerCtrl) {
        this.nativePointerListener.clearPointerHandlingOnLayer(layer);
    }

    attachViewport(vm: ViewportManager) {
        this.vms.push(vm);
        vm.registerLayerEventListener(this.layerEventListener); // listen for layer events to attach listener
        this.captureEventsFor(vm.rootEl); // capture keyboard event from the viewport
    }

    detachViewport(vm: ViewportManager) {
        this.uncaptureEventsFor(vm.rootEl);
        vm.unregisterLayerEventListener(this.layerEventListener);
        this.focusedVM = this.focusedVM.filter(v => v != vm);
        this.vms = this.vms.filter(v => v != vm);
    }

    addFocusPoint(el: Element, editor: DocumentEditor, focusListener: VEventListener<EditorFocusEventData>) {
        throw new Error('Method not implemented.');
    }

    removeFocusPoint(el: Element, editor: DocumentEditor) {
        throw new Error('Method not implemented.');
    }

    isEnable(): boolean {
        return this._enabled;
    }

    enable() {
        this._enabled = true;
        // connect the external source with the listener
        this.extElSrc.nativeKeyboardEventEmitter.registerListener(this.nativeKeyboardListener);
    }

    disable() {
        this._enabled = false;
        // connect the external source with the listener
        this.extElSrc.nativeKeyboardEventEmitter.unregisterListener(this.nativeKeyboardListener);
        this.nativePointerListener.clearAllTracking();
    }

    captureAllKeyboardEvent<Source extends NativeEventTarget<Source>>(
        el: Element,
        handler: KeyboardEventListener<NativeEventTarget<Source>>
    ) {
        this.nativeKeyboardListener.captureAllKeyboardEvent(el, handler);
    }

    unCaptureAllKeyboardEvent(el: Element) {
        this.nativeKeyboardListener.unCaptureAllKeyboardEvent(el);
    }

    captureEventsFor(el: HTMLElement | SVGElement) {
        this.extElSrc.captureKeyboardEventFor([el]);
    }

    uncaptureEventsFor(el: HTMLElement | SVGElement) {
        this.extElSrc.uncaptureKeyboardEventFor([el]);
    }

    registerMouseHandling(handlingItem: MouseHandlingItem, handler: MouseEventListener<NativeEventTarget<any>>) {
        this.nativeMouseListener.registerMouseHandling(handlingItem, handler);
    }

    registerPointerHandling(handlingItem: PointerHandlingItem, handler: PointerEventListener<NativeEventTarget<any>>) {
        this.nativePointerListener.registerPointerHandling(handlingItem, handler);
    }

    registerMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>) {
        this.nativeMouseListener.registerMouseInterceptor(handler);
    }

    unregisterMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>) {
        this.nativeMouseListener.unregisterMouseInterceptor(handler);
    }

    registerPostMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>) {
        this.nativeMouseListener.registerPostMouseInterceptor(handler);
    }

    unregisterPostMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>) {
        this.nativeMouseListener.unregisterPostMouseInterceptor(handler);
    }

    registerPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>) {
        this.nativePointerListener.registerPointerInterceptor(handler);
    }

    unregisterPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>) {
        this.nativePointerListener.unregisterPointerInterceptor(handler);
    }

    registerPostPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>) {
        this.nativePointerListener.registerPostPointerInterceptor(handler);
    }

    unregisterPostPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>) {
        this.nativePointerListener.unregisterPostPointerInterceptor(handler);
    }

    registerKeyboardHandling(
        handlingItem: KeyboardHandlingItem,
        handler: KeyboardEventListener<NativeEventTarget<any>>
    ) {
        this.nativeKeyboardListener.registerKeyboardHandling(handlingItem, handler);
    }

    resetMouseHandling() {
        this.nativeMouseListener.resetMouseHandling();
    }

    resetPointerHandling() {
        this.nativePointerListener.resetPointerHandling();
    }

    resetKeyboardHandling() {
        this.nativeKeyboardListener.resetKeyboardHandling();
    }
}

export class WrappedEventListener implements VEventListener<VEventData<any, any, any>> {
    constructor(private listener: VEventListener<any>) {}

    onEvent(eventData: VEventData<any, any, any>): VEventData<any, any, any> | Promise<VEventData<any, any, any>> {
        if (eventData.processedHandlers?.includes(this.listener)) return eventData;
        eventData.processedHandlers = eventData.processedHandlers || [];
        eventData.processedHandlers.push(this.listener);
        return this.listener.onEvent(eventData);
    }
}
