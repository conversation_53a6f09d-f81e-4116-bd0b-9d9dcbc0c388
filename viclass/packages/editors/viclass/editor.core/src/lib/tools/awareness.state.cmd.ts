/**
 * Awareness State Commands
 *
 * This module defines commands for sending and clearing awareness states between users.
 * It includes serialization and deserialization of awareness states for network transmission.
 */
import { ClearAwarenessStateProto, CmdTypeProto, SendAwarenessStateProto } from '@viclass/proto/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { AbstractCommand, Cmd, CmdMeta, CriticalErr, reliableCmdMeta, ViewportManager } from '../api';
import { Awareness, AwarenessCmdOption, AwarenessType } from './awareness.tool';

/**
 * Command for sending an awareness state to other users
 * Handles serialization and deserialization of awareness states
 */
export class SendAwarenessStateCmd extends AbstractCommand<CmdTypeProto> {
    /**
     * Protocol buffer state for this command
     */
    state: SendAwarenessStateProto;

    /**
     * Creates a new send awareness state command
     *
     * @param meta - Metadata for this command
     */
    constructor(meta: CmdMeta) {
        super(meta, CmdTypeProto.SEND_AWARENESS_STATE);

        this.state = new SendAwarenessStateProto();
    }

    /**
     * Deserializes a binary buffer into a protocol buffer state
     *
     * @param buf - Binary buffer containing the serialized state
     * @returns The deserialized protocol buffer state
     */
    deserialize(buf: Uint8Array): SendAwarenessStateProto {
        return SendAwarenessStateProto.deserializeBinary(buf);
    }

    /**
     * Serializes the protocol buffer state into a binary buffer
     *
     * @returns Binary buffer containing the serialized state
     */
    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    /**
     * Creates a send awareness state command from an awareness object
     *
     * @param vp - The viewport manager that is sending the awareness
     * @param awareness - The awareness state to send
     * @returns A new send awareness state command
     */
    static fromAwareness(vp: ViewportManager, awareness: Awareness): SendAwarenessStateCmd {
        // Create a new command with reliable metadata
        const cmd = new SendAwarenessStateCmd(reliableCmdMeta(vp, 0, -1, CmdTypeProto.SEND_AWARENESS_STATE, true));

        // Set the basic awareness properties
        cmd.state.setCoordinatorId(awareness.viewportId);
        if (awareness.options.id) cmd.state.setId(awareness.options.id);
        cmd.state.setType(awareness.options.type);
        cmd.state.setUseScheduler(!!awareness.options.useScheduler);
        cmd.state.setExpireAfterSeconds(awareness.options.expireAfterSeconds || 0);
        cmd.state.setMessage(awareness.message);

        // Set document-specific properties if this is a document awareness
        if (awareness.options.type === 'aw-document') {
            cmd.state.setDocLocalId(awareness.options.docInfo.localId);
            cmd.state.setDocGlobalId(awareness.options.docInfo.globalId || '');
        }

        // Set optional properties if they exist
        if (awareness.userId) cmd.state.setUserId(awareness.userId);
        if (awareness.options.payload) cmd.state.setData(JSON.stringify(awareness.options.payload));

        return cmd;
    }

    /**
     * Converts the protocol buffer state back into an awareness object
     *
     * @returns The awareness object represented by this command
     * @throws Error if a document awareness is missing required properties
     */
    toAwareness() {
        const type = this.state.getType() as AwarenessType;
        // Validate that document awareness has the required document ID
        if (type === 'aw-document' && !this.state.hasDocLocalId()) {
            throw new Error('Document awareness state has no local id');
        }

        // Create an awareness object from the protocol buffer state
        const awareness: Awareness = {
            viewportId: this.state.getCoordinatorId(),
            message: this.state.getMessage(),
            userId: this.state.hasUserId() ? this.state.getUserId() : undefined,
            updatedAt: performance.now(), // Use current time as the update time
            options: {
                id: this.state.hasId() ? this.state.getId() : undefined,
                useScheduler: this.state.getUseScheduler(),
                expireAfterSeconds: this.state.getExpireAfterSeconds(),
                payload: this.state.hasData() ? JSON.parse(this.state.getData()) : undefined,
                type,
                // Include document info only for document awareness
                docInfo:
                    type === 'aw-document'
                        ? {
                              localId: this.state.getDocLocalId(),
                              globalId: this.state.hasDocGlobalId() ? this.state.getDocGlobalId() : undefined,
                          }
                        : undefined,
            } as AwarenessCmdOption,
        };

        return awareness;
    }
}

/**
 * Command for clearing an awareness state
 * Used to remove an awareness state that is no longer relevant
 */
export class ClearAwarenessStateCmd extends AbstractCommand<CmdTypeProto> {
    /**
     * Protocol buffer state for this command
     */
    state: ClearAwarenessStateProto;

    /**
     * Creates a new clear awareness state command
     *
     * @param meta - Metadata for this command
     */
    constructor(meta: CmdMeta) {
        super(meta, CmdTypeProto.CLEAR_AWARENESS_STATE);

        this.state = new ClearAwarenessStateProto();
    }

    /**
     * Deserializes a binary buffer into a protocol buffer state
     *
     * @param buf - Binary buffer containing the serialized state
     * @returns The deserialized protocol buffer state
     */
    deserialize(buf: Uint8Array): ClearAwarenessStateProto {
        return ClearAwarenessStateProto.deserializeBinary(buf);
    }

    /**
     * Serializes the protocol buffer state into a binary buffer
     *
     * @returns Binary buffer containing the serialized state
     */
    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }
}

/**
 * Deserializes awareness-related commands from binary data
 * Factory function that creates the appropriate command object based on the command type
 *
 * @param meta - Metadata for the command
 * @param stateData - Binary data containing the serialized command state
 * @returns The deserialized command
 * @throws CriticalErr if the command type is unknown
 */
export function awarenessCoordCmdDeserializer(
    meta: CmdMeta,
    stateData: Uint8Array
): Cmd<CmdTypeProto | FCCmdTypeProto> {
    let cmd: Cmd<CmdTypeProto | FCCmdTypeProto>;
    const cmdType = meta.cmdType as CmdTypeProto | FCCmdTypeProto;

    // Create the appropriate command object based on the command type
    switch (cmdType) {
        case CmdTypeProto.SEND_AWARENESS_STATE: {
            cmd = new SendAwarenessStateCmd(meta);
            break;
        }

        case CmdTypeProto.CLEAR_AWARENESS_STATE: {
            cmd = new ClearAwarenessStateCmd(meta);
            break;
        }

        default: {
            throw new CriticalErr(`Unknown cmd type: ${cmdType}`);
        }
    }

    // Deserialize the binary data into the command state
    cmd.state = cmd.deserialize(stateData);

    return cmd;
}
