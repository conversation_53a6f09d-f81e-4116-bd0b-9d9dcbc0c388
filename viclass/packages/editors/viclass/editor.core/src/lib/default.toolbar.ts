import {
    ChangeTool<PERSON>ventD<PERSON>,
    CoordinatorE<PERSON>,
    CoordinatorEventType,
    DefaultToolEventData,
    EditorCoordinator,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    PointerEventListener,
    ProxyObjectConfig,
    Tool,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    ToolState,
    UserInputHandlerType,
    VEventListener,
    ViewportManager,
    ViewportMode,
} from './api';
import { DefaultEventEmitter } from './default.event.source';

export abstract class DefaultToolBar<ToolType, ToolImpl extends Tool> implements ToolBar<ToolType, ToolImpl> {
    readonly type: UserInputHandlerType = 'Toolbar';
    readonly toolbarsState: Map<ToolType, ToolState> = new Map();
    readonly tools: Map<ToolType, ToolImpl> = new Map();

    private readonly disabledTool: Map<ToolType, boolean> = new Map();

    private toolbarDisabled = true;

    private _curTool?: ToolType;
    private _activeTool?: ToolImpl;
    private activeStack: ToolType[] = [];

    private _viewport: ViewportManager | undefined = undefined;

    abstract readonly keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    abstract readonly mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    abstract readonly pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    protected readonly coordEventListener: VEventListener<CoordinatorEvent>;

    mouseHandling: MouseHandlingItem[] = [];
    keyboardHandling: KeyboardHandlingItem[] = [];

    readonly eventSource: DefaultEventEmitter<ToolEventData<ToolBar<ToolType, ToolImpl>, ToolType>> =
        new DefaultEventEmitter();

    constructor(protected coord: EditorCoordinator) {
        this.coordEventListener = this.generateCoordEventListener();
    }

    /**
     * Call this method when need toolbar handle some internal logic when viewport mode is changed.
     * The internal logic need to be handled before receive 'viewport mode event' cause HAM is received this event
     * before the toolbar receive them.
     * Ex: when need to enable/disable tool, the editor need to disable/enable tools before HAM handle
     * 'viewport mode event' to make sure the event is distributed correctly to each tool.
     * @param vpMode
     */
    protected abstract onViewportModeChanged(vpMode: ViewportMode): Promise<void>;

    protected generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: DefaultToolBar<any, any>) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState?.vmId !== this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-edit-mode': {
                        this.toolbar.onViewportModeChanged('EditMode');
                        break;
                    }
                    case 'viewport-interactive-mode': {
                        this.toolbar.onViewportModeChanged('InteractiveMode');
                        break;
                    }
                    case 'viewport-view-mode': {
                        this.toolbar.onViewportModeChanged('ViewMode');
                        break;
                    }
                    case 'viewport-disabled': {
                        this.toolbar.onViewportModeChanged('Disabled');
                        break;
                    }
                    case 'viewport-removed': {
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    async clearAllFocus() {
        const promises: Promise<void>[] = [];

        while (this._curTool) {
            if (this._activeTool?.childToolbar) {
                const clearRes = this._activeTool.childToolbar.clearAllFocus();
                if (clearRes && clearRes instanceof Promise) promises.push(clearRes);
            }

            const res = this.blur(this._curTool);
            if (res && res instanceof Promise) promises.push(res);
        }

        await Promise.all(promises);
    }

    isToolDisable(toolType: ToolType) {
        return this.toolbarDisabled || !this.tools.has(toolType) || this.disabledTool.get(toolType) === true;
    }

    disableTool(toolType: ToolType) {
        if (this.disabledTool.get(toolType)) return;

        const t = this.getTool(toolType);
        if (t?.childToolbar) t.childToolbar.clearAllFocus();

        if (this.isToolActive(toolType)) this.blur(toolType);

        this.disabledTool.set(toolType, true);
        if (t) t.onDisable();

        this.eventSource.emit(new DefaultToolEventData('tool-disabled', this, toolType));
    }

    enableTool(toolType: ToolType) {
        if (!this.disabledTool.get(toolType)) return;
        this.disabledTool.set(toolType, false);

        const t = this.getTool(toolType);
        if (t) t.onEnable();

        this.eventSource.emit(new DefaultToolEventData('tool-enabled', this, toolType));
    }

    disable() {
        if (this.toolbarDisabled) return;
        this.clearAllFocus();
        this.toolbarDisabled = true;
        this.eventSource.emit(new DefaultToolEventData('toolbar-disabled', this, undefined));
    }

    enable() {
        if (!this.toolbarDisabled) return;
        this.toolbarDisabled = false;
        this.eventSource.emit(new DefaultToolEventData('toolbar-enabled', this, undefined));
    }

    isDisabled(): boolean {
        return this.toolbarDisabled;
    }

    get empty(): boolean {
        return this.tools.size === 0;
    }

    get curTool(): ToolType | undefined {
        return this._curTool;
    }

    get activeTool(): ToolImpl | undefined {
        return this._activeTool;
    }

    get viewport(): ViewportManager | undefined {
        return this._viewport;
    }

    set viewport(vm: ViewportManager | undefined) {
        this._viewport = vm;
        for (const tool of this.tools.values()) {
            if (tool.childToolbar) tool.childToolbar.viewport = vm;
        }
    }

    getTool(toolName: ToolType): ToolImpl | undefined {
        return this.tools.get(toolName);
    }

    addTool(toolType: ToolType, tool: ToolImpl) {
        tool.toolbar = this;
        this.tools.set(toolType, tool);
        this.disabledTool.set(toolType, true);

        if (tool.toolState) this.toolbarsState.set(toolType, tool.toolState);
    }

    mouseHandlingItems(toolName: ToolType): MouseHandlingItem[] {
        const tool = this.tools.get(toolName);
        if (tool?.mouseHandling) return tool.mouseHandling;
        return [];
    }

    keyboardHandlingItems(toolType: ToolType): KeyboardHandlingItem[] {
        const tool = this.tools.get(toolType);
        if (tool?.keyboardHandling) return tool.keyboardHandling;
        return [];
    }

    registerToolListener(listener: ToolEventListener<ToolBar<ToolType, ToolImpl>, ToolType>) {
        this.eventSource.registerListener(listener);
    }

    unregisterToolListener(listener: ToolEventListener<ToolBar<ToolType, ToolImpl>, ToolType>) {
        this.eventSource.unregisterListener(listener);
    }

    async focus(toolName: ToolType, transient?: boolean) {
        const activeTool = this.tools.get(toolName);
        if (this.isDisabled() || !activeTool || this.isToolDisable(toolName) || this.curTool === toolName) return;

        if (this.curTool) {
            // if a tool is transiently focus, the current active tool of the toolbar will be push into a stack so that its active status can be restored later
            if (transient) this.activeStack.push(this.curTool);
            else this.blur(this.curTool, false);
        }

        this._activeTool = activeTool;
        this._curTool = toolName;

        this.viewport?.rootEl?.focus({ preventScroll: true });
        this._activeTool.onFocus();

        if (!transient) this.eventSource.emit(new DefaultToolEventData('focus', this, toolName));
        else this.eventSource.emit(new DefaultToolEventData('transient-focus', this, toolName));
    }

    async blur(toolName: ToolType, transient?: boolean) {
        if (!this.curTool) return;
        if (this.isDisabled() || this.isToolDisable(toolName)) return;

        if (this.isToolActive(toolName)) {
            this._curTool = undefined;
            const t = this._activeTool;
            this._activeTool = undefined;

            await t?.onBlur();

            if (transient && this.activeStack.length) {
                // reset the active status of the last active tool
                this._curTool = this.activeStack[this.activeStack.length - 1];
                this.activeStack.splice(this.activeStack.length - 1, 1);
                this._activeTool = this.getTool(this._curTool);
            }
        }

        await this.eventSource.emitSync(
            new DefaultToolEventData(transient ? 'transient-blur' : 'blur', this, toolName)
        );
    }

    update<T extends ToolState>(toolType: ToolType, toolState: T | undefined) {
        if (toolState) this.toolbarsState.set(toolType, toolState);

        let event: ToolEventData<any, any>;
        if (toolState?.['_proxied'] !== undefined) {
            const proxiedToolState = toolState as unknown as ProxyObjectConfig<any>;
            const proxiedObj = proxiedToolState._proxyObj!;
            event = new ChangeToolEventData(this, toolType, proxiedObj.changes);
        } else event = new DefaultToolEventData('change', this, toolType);
        event.state = toolState;

        this.eventSource.emit(event);
    }

    toolState<T extends ToolState>(toolName: ToolType): T {
        return this.toolbarsState.get(toolName) as T;
    }

    isToolActive(tool: ToolType) {
        return this.curTool === tool;
    }

    attachViewport(viewport: ViewportManager) {
        this.clearAllFocus(); // clear all focus if any

        this.viewport = viewport;

        for (const type of this.disabledTool.keys()) this.disableTool(type);

        this.tools.forEach((tool, type) => tool.onAttachViewport());

        this.coord.registerCoordEventListener(this.coordEventListener);

        const e = new DefaultToolEventData('toolbar-viewport-attach', this, undefined);
        e.state = viewport;
        this.eventSource.emit(e);
    }

    detachViewport(viewport: ViewportManager) {
        if (this.viewport !== viewport)
            throw new Error('Something wrong! Trying to detach a viewport not yet attached to this toolbar');

        this.coord.unregisterCoordEventListener(this.coordEventListener);

        this.tools.forEach((tool, type) => {
            tool.onDetachViewport();
        });

        this.clearAllFocus();

        this.viewport = undefined;

        const e = new DefaultToolEventData('toolbar-viewport-detach', this, undefined);
        e.state = viewport;
        this.eventSource.emit(e);
    }
}
