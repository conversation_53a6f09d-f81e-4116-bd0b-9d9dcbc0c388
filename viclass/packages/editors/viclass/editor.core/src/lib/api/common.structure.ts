export interface Position {
    x: number;
    y: number;
    z?: number;
}

export interface RectSize {
    width: number;
    height: number;
}

export type Vector = Position;

/**
 * Screen position is a position relative to the (0,0) point of the viewport
 */
export type ScreenPosition = Position;

export interface Rectangle {
    start: Position;
    end: Position;
}

/**
 * BoundaryRectangle is a specification of a boundary
 */
export interface BoundaryRectangle {
    start?: Position;
    end?: Position;
    width?: number;
    height?: number;
}

export interface ValueChange<T> {
    previousValue: T;
    currentValue: T;
    propertyName: string;
}

export interface ProxyObjectConfig<T extends ProxyObjectConfig<T>> {
    _proxied: boolean;
    _proxyObj?: ProxyObject<T>; // this will be set when proxy instance is created

    listenProps(prop: string): boolean;
}

export class ProxyObject<T extends ProxyObjectConfig<T>> {
    readonly proxyInstance: T;
    private _changes: Map<string, ValueChange<any>> = new Map();

    constructor(public original: T) {
        this.proxyInstance = this.createProxyInstance();
        original._proxyObj = this;
    }

    private createProxyInstance(): T {
        const outer = this;
        const handler: ProxyHandler<any> = {
            set(obj: T, prop: string, value: any): boolean {
                if (outer.original.listenProps(prop)) {
                    outer._changes.set(prop, {
                        propertyName: prop,
                        previousValue: obj[prop],
                        currentValue: value,
                    });
                }
                return Reflect.set(obj, prop, value, obj);
            },
        };
        return new Proxy(this.original, handler);
    }

    get changes() {
        const tmp = this._changes;
        this._changes = new Map();
        return tmp;
    }
}
