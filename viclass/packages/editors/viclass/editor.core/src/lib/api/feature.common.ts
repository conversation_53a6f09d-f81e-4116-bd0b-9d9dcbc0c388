/**
 * This module specify common features that supported by most editors
 */

import { CRUDHookOptions } from './command';
import { BoundaryRectangle, Position } from './common.structure';
import { Cmd } from './data.model';
import {
    CreatingContext,
    CreatingLayer<PERSON>ontext,
    DocLocalContent,
    DocumentEditor,
    EditorBackendConnector,
} from './editor';
import { ViewportManager } from './viewport';

// ------ CRUD FEATURES FOR DOC AND LAYER ----

export const FEATURE_CRUD = 'feature_crud';

export type CRUDCmdType =
    | 'create-doc'
    | 'remove-doc'
    | 'insert-doc'
    | 'update-boundary'
    | 'local-content-update'
    | 'create-layer';
export type CRUDCmdOption =
    | {
          cmdType: Extract<CRUDCmdType, 'create-doc'>;
          ctx: CreatingContext;
          waitGlobalIdAvailable: boolean;
      }
    | ((
          | {
                cmdType: Extract<CRUDCmdType, 'insert-doc'>;
                layers: (LayerPrepInfo[] | undefined)[];
                loadingCtx?: EditorBackendConnector;
                origin?: Position;
                baseBoundary?: BoundaryRectangle;
                docName?: string; // optional, original doc name
            }
          | {
                cmdType: Extract<CRUDCmdType, 'remove-doc'>;
            }
          | {
                cmdType: Extract<CRUDCmdType, 'update-boundary'>;
                boundaries: BoundaryRectangle[];
                data?: any;
            }
      ) & {
          vm: ViewportManager;
          ids: DocPrepInfo[];
      })
    | {
          cmdType: Extract<CRUDCmdType, 'local-content-update'>;
          vm: ViewportManager;
          ids: DocPrepInfo[];
      }
    | {
          cmdType: Extract<CRUDCmdType, 'create-layer'>;
          ctx: CreatingLayerContext;
      };

// when doing something with layer, this is the expected changes
export interface CRUDExpectedLayerChange {
    layerId: number; // local id of the layer that will be created
    boundary?: BoundaryRectangle; // depending on cases, the layer might have a boundary or not having a boundary
    zindex?: number; // position of the layer in the stack of layers within the viewport
}

export interface DocPrepInfo {
    localId: number;
    globalId?: string;
    localContent?: DocLocalContent;
    docName?: string;
}

export interface LayerPrepInfo {
    boundary?: BoundaryRectangle; // depending on cases, the layer might have a boundary or not having a boundary
    zindex?: number; // position of the layer in the stack of layers within the viewport
}
export interface CRUDLayerPrepInfo {
    added?: CRUDExpectedLayerChange[]; // list of information of new layers which will be added (or has been added LOCALLY)
    updated?: CRUDExpectedLayerChange[]; // list of information of existing layers which will be updated (or has been updated LOCALLY)
    removed?: CRUDExpectedLayerChange[]; // list of information of old layers which will be removed (or has been removed LOCALLY)
}

export type CRUDChangeData = {
    expectedChanges: (DocPrepInfo & {
        // an array of new doc info, one for each new doc being created
        layerChanges: CRUDLayerPrepInfo;
    })[];
};

export type CRUDChangeCommon = {
    editor: DocumentEditor;
    edCmds: Cmd<any>[]; // list of commands that will be actually executed by the editor
    vmId: string; // the viewport that these commands will be executed on
    options: CRUDCmdOption;
};

export type NewDocChangeResult = CRUDChangeCommon &
    (
        | {
              cmdType: Extract<CRUDCmdType, 'create-doc'>;
          }
        | {
              cmdType: Extract<CRUDCmdType, 'insert-doc'>;
              original: DocPrepInfo[];
          }
    ) &
    CRUDChangeData;

export type UpdateDocChangeResult = {
    cmdType: Exclude<CRUDCmdType, 'create-doc' | 'insert-doc'>;
} & CRUDChangeData &
    CRUDChangeCommon;

export type LocalContentUpdateResult = CRUDChangeCommon & {
    cmdType: Extract<CRUDCmdType, 'local-content-update'>;
    coordCmds: Cmd<any>[]; // list of commands that will be actually executed by the coord
    expectedChanges: (DocPrepInfo & {
        // The updated local content
        localContent: DocLocalContent;
    })[];
};

export type CRUDChangeResult = NewDocChangeResult | UpdateDocChangeResult | LocalContentUpdateResult;

export interface SupportFeatureCRUD {
    prepareCRUDCmd(options: CRUDCmdOption): Promise<CRUDChangeResult>; // prepare the command that will be apply to the editor through command channel to complete the changes

    // applying the preparation result return the changes if successful, otherwise the promise fail
    // the party that invoke the supporter to apply the cmd can also provide hooks which will be invoked
    // when commands processed
    applyCRUDCmd(changes: CRUDChangeResult, cmdHook?: CRUDHookOptions): Promise<CRUDChangeResult>;
}
