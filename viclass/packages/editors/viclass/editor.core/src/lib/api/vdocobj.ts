import { Rectangle } from './common.structure';
import { IdAble } from './data.model';
import { DocumentEditor } from './editor';
import { NativeEventTarget } from './events';
import { VDocCtrl } from './vdoc';

/**
 * Data of a document object
 */

export interface VDocObj extends IdAble {}

/**
 * This is a view controller contain the document object data. Each document object created will be wrapped by the controller
 * The controller provides manipulation of the object render data in response to the external command and render the object on the screen.
 * For example in the case of the object is an HTML element, it will be responsible
 * for setting the style of the element appropriately, or in the case of pixel element, it provide ways to render
 * the object from the object data on canvas or convert from object data to html elements
 *
 */
export interface VDocObjCtrl<T extends VDocObj> extends NativeEventTarget<VDocObjCtrl<T>> {
    /**
     * Boundary of the object, this is used for displaying
     */
    boundary: Rectangle;

    editor: DocumentEditor;

    document: VDocCtrl;

    /**
     * If this is a html object, there should be a native element
     * attached to it
     */
    nativeEl?: Element;

    /**
     * Whether an object is selectable
     */
    isSelectable: boolean;
}
