import { WordForwardHistoryManager } from './forward.history.manager';
import { WordDocCtrl } from '../docs/word.doc.ctrl';
import { WordEditor } from '../word.editor';
import { HistoryFeature, HistoryManager } from '@viclass/editor.core';
import { WordEditorCoordinator } from './word.coordinator';

/**
 * Special history feature for word coordinator, which will forward the history of sub editors
 * to the history of the main word editor by always return the HistoryManager of main editor
 */
export class WordForwardHistoryFeature extends HistoryFeature {
    get wcoord(): WordEditorCoordinator {
        return this.coord as WordEditorCoordinator;
    }

    constructor(
        coord: WordEditorCoordinator,
        private weditor: WordEditor
    ) {
        super(coord);
    }

    override getHistoryManager(vpId: string): HistoryManager {
        const docCtrl = this.getWordDocCtrl(vpId);
        if (!docCtrl) throw Error(`Don't have word doc ctrl of viewport ${vpId}`);

        const rootVmId = docCtrl.viewport.id;
        let manager = this.historyManagers.get(rootVmId);
        if (!manager) {
            const targetHistoryManager = this.weditor.historyFeature.getHistoryManager(rootVmId);

            // create a dummy manager that will forward the history item to the main vp HistoryManager
            manager = new WordForwardHistoryManager(targetHistoryManager, this.weditor);
            this.historyManagers.set(rootVmId, manager);
        }

        return manager;
    }

    private getWordDocCtrl(vpId: string): WordDocCtrl {
        return this.wcoord.containingDoc.get(vpId);
    }
}
