import { BoundaryRectangle, Position } from '@viclass/editor.core';
import {
    BoundaryProto,
    CoordsProto,
    DocDefaultElRenderPropsProto,
    DocRenderPropProto,
    In32Wrapper,
    LineTypeProto,
    PositionProto,
    PreviewAngleProto,
    PreviewCircleShapeProto,
    PreviewEllipseShapeProto,
    PreviewLineProto,
    PreviewPolygonProto,
    PreviewSectorShapeProto,
    PreviewVertexProto,
    RenderAngleProto,
    RenderCircleProto,
    RenderCircleShapeProto,
    RenderEllipseProto,
    RenderEllipseShapeProto,
    RenderLineProto,
    RenderPolygonProto,
    RenderSectorProto,
    RenderSectorShapeProto,
    RenderVertexProto,
} from '@viclass/proto/editor.geo';
import {
    AngleGeoRenderProp,
    CircleGeoRenderProp,
    CircleShapeGeoRenderProp,
    DefaultGeoRenderProp,
    DocRenderProp,
    EllipseGeoRenderProp,
    EllipseShapeGeoRenderProp,
    GeoRelType,
    GeoStrokeStyle,
    LineGeoRenderProp,
    LineSegmentGeoRenderProp,
    PointGeoRenderProp,
    PolygonGeoRenderProp,
    PreviewAngle,
    PreviewCircleShape,
    PreviewEllipseShape,
    PreviewLine,
    PreviewPolygon,
    PreviewSectorShape,
    PreviewVertex,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
    SectorGeoRenderProp,
    SectorShapeGeoRenderProp,
} from '../model';
import { GeoObjectType } from '../model/geo.models';
import { isEmpty } from '../tools/tool.utils';

export function buildPositionProto(point: Position): PositionProto {
    return new PositionProto().setX(point.x).setY(point.y).setZ(point.z);
}

export function convertProtoToPosition(proto: PositionProto): Position {
    return { x: proto.getX(), y: proto.getY(), z: proto.getZ() };
}

export function buildBoundaryProto(boundary: BoundaryRectangle): BoundaryProto {
    return new BoundaryProto()
        .setStart(new PositionProto().setX(boundary.start.x).setY(boundary.start.y))
        .setEnd(new PositionProto().setX(boundary.end.x).setY(boundary.end.y));
}

export function convertProtoToBoundary(boundary: BoundaryProto): BoundaryRectangle {
    const start = {
        x: boundary.getStart().getX(),
        y: boundary.getStart().getY(),
    };
    const end = { x: boundary.getEnd().getX(), y: boundary.getEnd().getY() };
    return {
        start: start,
        end: end,
        width: Math.abs(end.x - start.x),
        height: Math.abs(end.y - start.y),
    };
}

export function buildCoordsProto(coords: number[]): CoordsProto {
    return new CoordsProto().setCoordsList(coords);
}

export function convertProtoToCoords(proto: CoordsProto): number[] {
    return proto.getCoordsList();
}

export function buildRenderVertexProto(vertex: RenderVertex): RenderVertexProto {
    return new RenderVertexProto()
        .setRenderProp(vertex.renderProp.toRenderPropProto())
        .setName(vertex.name)
        .setRelIndex(vertex.relIndex)
        .setMovementPath(JSON.stringify(vertex.movementPath))
        .setCoords(buildCoordsProto(vertex.coords))
        .setUsable(vertex.usable)
        .setDeleted(vertex.deleted)
        .setValid(vertex.valid)
        .setArea(vertex.area)
        .setPerimeter(vertex.perimeter);
}

export function buildPreviewVertexProto(vertex: PreviewVertex): PreviewVertexProto {
    return new PreviewVertexProto()
        .setRenderProp(vertex.renderProp.toRenderPropProto())
        .setName(vertex.name)
        .setRelIndex(vertex.relIndex)
        .setCoords(buildCoordsProto(vertex.coords))
        .setArea(vertex.area)
        .setPerimeter(vertex.perimeter)
        .setUnselectable(vertex.unselectable)
        .setValid(vertex.valid)
        .setUsable(vertex.usable);
}

export function convertProtoToRenderVertex(proto: RenderVertexProto): RenderVertex {
    const vertex = new RenderVertex(Object.assign(new PointGeoRenderProp(), proto.getRenderProp().toObject()));
    vertex.name = proto.getName();
    vertex.coords = convertProtoToCoords(proto.getCoords());
    vertex.relIndex = proto.getRelIndex();
    vertex.movementPath = JSON.parse(proto.getMovementPath());
    vertex.usable = proto.getUsable();
    vertex.deleted = proto.getDeleted();
    vertex.valid = proto.getValid();
    vertex.area = proto.getArea();
    vertex.perimeter = proto.getPerimeter();
    return vertex;
}

export function convertProtoToPreviewVertex(proto: PreviewVertexProto): PreviewVertex {
    const vertex = new PreviewVertex(Object.assign(new PointGeoRenderProp(), proto.getRenderProp().toObject()));
    vertex.name = proto.getName();
    vertex.coords = convertProtoToCoords(proto.getCoords());
    vertex.relIndex = proto.getRelIndex();
    vertex.area = proto.getArea();
    vertex.perimeter = proto.getPerimeter();
    vertex.unselectable = proto.getUnselectable();
    vertex.valid = proto.getValid();
    vertex.usable = proto.getUsable();

    return vertex;
}

export function buildRenderAngleProto(angle: RenderAngle): RenderAngleProto {
    return new RenderAngleProto()
        .setRenderProp(angle.renderProp.toRenderPropProto())
        .setName(angle.name)
        .setRelIndex(angle.relIndex)
        .setVertexRelIdxesList(angle.vertexRelIdxes)
        .setLineRelIdxesList(angle.lineRelIdxes)
        .setAnglePointIdxes(angle.anglePointIdx)
        .setVStart(buildCoordsProto(angle.vectorStart))
        .setVEnd(buildCoordsProto(angle.vectorEnd))
        .setUsable(angle.usable)
        .setDeleted(angle.deleted)
        .setValid(angle.valid)
        .setArea(angle.area)
        .setPerimeter(angle.perimeter)
        .setDegree(angle.degree);
}

export function buildPreviewAngleProto(angle: PreviewAngle): PreviewAngleProto {
    return new PreviewAngleProto()
        .setRenderProp(angle.renderProp.toRenderPropProto())
        .setRelIndex(angle.relIndex)
        .setName(angle.name)
        .setAnglePoint(buildCoordsProto(angle.anglePoint))
        .setStartPoint(buildCoordsProto(angle.startPoint))
        .setEndPoint(buildCoordsProto(angle.endPoint))
        .setVStart(buildCoordsProto(angle.vStart))
        .setVEnd(buildCoordsProto(angle.vEnd))
        .setArea(angle.area)
        .setPerimeter(angle.perimeter)
        .setUnselectable(angle.unselectable)
        .setValid(angle.valid)
        .setUsable(angle.usable);
}

export function convertProtoToRenderAngle(proto: RenderAngleProto): RenderAngle {
    const angle = new RenderAngle(Object.assign(new AngleGeoRenderProp(), proto.getRenderProp().toObject()));
    angle.name = proto.getName();
    angle.relIndex = proto.getRelIndex();
    angle.vertexRelIdxes = proto.getVertexRelIdxesList();
    angle.lineRelIdxes = proto.getLineRelIdxesList();
    angle.anglePointIdx = proto.getAnglePointIdxes();
    angle.vectorStart = convertProtoToCoords(proto.getVStart());
    angle.vectorEnd = convertProtoToCoords(proto.getVEnd());
    angle.usable = proto.getUsable();
    angle.deleted = proto.getDeleted();
    angle.valid = proto.getValid();
    angle.area = proto.getArea();
    angle.perimeter = proto.getPerimeter();
    angle.degree = proto.getDegree();
    return angle;
}

export function convertProtoToPreviewAngle(proto: PreviewAngleProto): PreviewAngle {
    const angle = new PreviewAngle(Object.assign(new AngleGeoRenderProp(), proto.getRenderProp().toObject()));
    angle.relIndex = proto.getRelIndex();
    angle.name = proto.getName();
    angle.anglePoint = convertProtoToCoords(proto.getAnglePoint());
    angle.startPoint = convertProtoToCoords(proto.getStartPoint());
    angle.endPoint = convertProtoToCoords(proto.getEndPoint());
    angle.vStart = convertProtoToCoords(proto.getVStart());
    angle.vEnd = convertProtoToCoords(proto.getVEnd());
    angle.area = proto.getArea();
    angle.perimeter = proto.getPerimeter();
    angle.unselectable = proto.getUnselectable();
    angle.valid = proto.getValid();
    angle.usable = proto.getUsable();
    return angle;
}

export function buildLineTypeProto(lineType: GeoRelType): LineTypeProto {
    switch (lineType) {
        case 'RenderLine':
            return LineTypeProto.LINE;
        case 'RenderLineSegment':
            return LineTypeProto.SEGMENT;
        case 'RenderVector':
            return LineTypeProto.VECTOR;
        case 'RenderRay':
            return LineTypeProto.RAY;
        default:
            return LineTypeProto.UNKNOWN;
    }
}

export function convertProtoToLineType(proto: LineTypeProto): GeoRelType {
    switch (proto) {
        case LineTypeProto.LINE:
            return 'RenderLine';
        case LineTypeProto.VECTOR:
            return 'RenderVector';
        case LineTypeProto.SEGMENT:
            return 'RenderLineSegment';
        case LineTypeProto.RAY:
            return 'RenderRay';
        default:
            return null;
    }
}

export function buildRenderLineProto(line: RenderLine): RenderLineProto {
    const proto = new RenderLineProto()
        .setRenderProp(line.renderProp.toRenderPropProto())
        .setRelIndex(line.relIndex)
        .setVertexRelIdxesList(line.vertexRelIdxes)
        .setName(line.name)
        .setLineType(buildLineTypeProto(line.type))
        .setElType(line.elType)
        .setStartPointIdx(line.startPointIdx)
        .setVectorList(line.vector)
        .setUsable(line.usable)
        .setDeleted(line.deleted)
        .setValid(line.valid)
        .setArea(line.area)
        .setPerimeter(line.perimeter);

    if (isFinite(line.endPointIdx)) proto.setEndPointIdx(line.endPointIdx);
    return proto;
}

export function buildRenderLineSegmentProto(line: RenderLine): RenderLineProto {
    const proto = new RenderLineProto()
        .setRenderProp(line.renderProp.toRenderPropProto())
        .setRelIndex(line.relIndex)
        .setVertexRelIdxesList(line.vertexRelIdxes)
        .setName(line.name)
        .setLineType(buildLineTypeProto(line.type))
        .setElType(line.elType)
        .setStartPointIdx(line.startPointIdx)
        .setVectorList(line.vector)
        .setUsable(line.usable)
        .setDeleted(line.deleted)
        .setValid(line.valid)
        .setArea(line.area)
        .setPerimeter(line.perimeter);

    if (isFinite(line.endPointIdx)) proto.setEndPointIdx(line.endPointIdx);
    return proto;
}

export function buildPreviewLineProto(line: PreviewLine): PreviewLineProto {
    const proto = new PreviewLineProto()
        .setRenderProp(line.renderProp.toRenderPropProto())
        .setRelIndex(line.relIndex)
        .setName(line.name)
        .setLineType(buildLineTypeProto(line.type))
        .setElType(line.elType)
        .setStartPoint(buildCoordsProto(line.startPoint))
        .setArea(line.area)
        .setPerimeter(line.perimeter)
        .setVector(buildCoordsProto(line.vector))
        .setValid(line.valid)
        .setUsable(line.usable)
        .setUnselectable(line.unselectable);

    if (line.endPoint) proto.setEndPoint(buildCoordsProto(line.endPoint));
    return proto;
}

export function convertProtoToRenderLine(proto: RenderLineProto): RenderLine {
    const line = new RenderLine(Object.assign(new LineGeoRenderProp(), proto.getRenderProp().toObject()));
    line.relIndex = proto.getRelIndex();
    line.vertexRelIdxes = proto.getVertexRelIdxesList();
    line.name = proto.getName();
    line.type = convertProtoToLineType(proto.getLineType());
    line.elType = proto.getElType() as GeoObjectType;
    line.startPointIdx = proto.getStartPointIdx();
    line.endPointIdx = proto.hasEndPointIdx() ? proto.getEndPointIdx() : null;
    line.vector = proto.getVectorList();
    line.usable = proto.getUsable();
    line.deleted = proto.getDeleted();
    line.valid = proto.getValid();
    line.area = proto.getArea();
    line.perimeter = proto.getPerimeter();
    return line;
}

export function convertProtoToRenderLineSegment(proto: RenderLineProto): RenderLine {
    const line = new RenderLineSegment(Object.assign(new LineSegmentGeoRenderProp(), proto.getRenderProp().toObject()));
    line.relIndex = proto.getRelIndex();
    line.vertexRelIdxes = proto.getVertexRelIdxesList();
    line.name = proto.getName();
    line.startPointIdx = proto.getStartPointIdx();
    line.endPointIdx = proto.getEndPointIdx();
    line.usable = proto.getUsable();
    line.deleted = proto.getDeleted();
    line.valid = proto.getValid();
    line.vector = proto.getVectorList();
    line.area = proto.getArea();
    line.perimeter = proto.getPerimeter();
    return line;
}

export function convertProtoToPreviewLine(proto: PreviewLineProto): PreviewLine {
    const line = new PreviewLine(Object.assign(new LineGeoRenderProp(), proto.getRenderProp().toObject()));
    line.relIndex = proto.getRelIndex();
    line.name = proto.getName();
    line.type = convertProtoToLineType(proto.getLineType());
    line.elType = proto.getElType() as GeoObjectType;
    line.startPoint = convertProtoToCoords(proto.getStartPoint());
    line.endPoint = proto.hasEndPoint() ? convertProtoToCoords(proto.getEndPoint()) : null;
    line.area = proto.getArea();
    line.perimeter = proto.getPerimeter();
    line.vector = convertProtoToCoords(proto.getVector());
    line.valid = proto.getValid();
    line.usable = proto.getUsable();
    line.unselectable = proto.getUnselectable();
    return line;
}

export function buildRenderCircleShapeProto(circle: RenderCircleShape): RenderCircleShapeProto {
    return new RenderCircleShapeProto()
        .setRenderProp(circle.renderProp.toRenderPropProto())
        .setName(circle.name)
        .setRelIndex(circle.relIndex)
        .setVertexRelIdxesList(circle.vertexRelIdxes)
        .setArcIdx(circle.arcRelIdx)
        .setRadius(circle.radius)
        .setCenterPointIdx(circle.centerPointIdx)
        .setUsable(circle.usable)
        .setDeleted(circle.deleted)
        .setValid(circle.valid)
        .setArea(circle.area)
        .setPerimeter(circle.perimeter);
}

export function buildPreviewCircleShapeProto(circle: PreviewCircleShape): PreviewCircleShapeProto {
    return new PreviewCircleShapeProto()
        .setRenderProp(circle.renderProp.toRenderPropProto())
        .setRelIndex(circle.relIndex)
        .setName(circle.name)
        .setRadius(circle.radius)
        .setCenterPoint(buildCoordsProto(circle.centerPoint))
        .setArea(circle.area)
        .setPerimeter(circle.perimeter)
        .setValid(circle.valid)
        .setUsable(circle.usable)
        .setUnselectable(circle.unselectable);
}

export function convertProtoToRenderCircleShape(proto: RenderCircleShapeProto): RenderCircleShape {
    const circle = new RenderCircleShape(
        Object.assign(new CircleShapeGeoRenderProp(), proto.getRenderProp().toObject())
    );
    circle.name = proto.getName();
    circle.relIndex = proto.getRelIndex();
    circle.vertexRelIdxes = proto.getVertexRelIdxesList();
    circle.arcRelIdx = proto.getArcIdx();
    circle.radius = proto.getRadius();
    circle.centerPointIdx = proto.getCenterPointIdx();
    circle.usable = proto.getUsable();
    circle.deleted = proto.getDeleted();
    circle.valid = proto.getValid();
    circle.area = proto.getArea();
    circle.perimeter = proto.getPerimeter();
    return circle;
}

export function convertProtoToPreviewCircleShape(proto: PreviewCircleShapeProto): PreviewCircleShape {
    const circle = new PreviewCircleShape(
        Object.assign(new CircleShapeGeoRenderProp(), proto.getRenderProp().toObject())
    );
    circle.relIndex = proto.getRelIndex();
    circle.name = proto.getName();
    circle.radius = proto.getRadius();
    circle.centerPoint = convertProtoToCoords(proto.getCenterPoint());
    circle.area = proto.getArea();
    circle.perimeter = proto.getPerimeter();
    circle.valid = proto.getValid();
    circle.usable = proto.getUsable();
    circle.unselectable = proto.getUnselectable();
    return circle;
}

export function buildRenderSectorShapeProto(circularSector: RenderSectorShape): RenderSectorShapeProto {
    return new RenderSectorShapeProto()
        .setRenderProp(circularSector.renderProp.toRenderPropProto())
        .setName(circularSector.name)
        .setRelIndex(circularSector.relIndex)
        .setElType(circularSector.elType)
        .setVertexRelIdxesList(circularSector.vertexRelIdxes)
        .setLineRelIdxesList(circularSector.lineRelIdxes)
        .setArcIdx(circularSector.arcRelIdx)
        .setCenterPointIdx(circularSector.centerPointIdx)
        .setStartPointIdx(circularSector.startPointIdx)
        .setEndPointIdx(circularSector.endPointIdx)
        .setRadius(circularSector.radius)
        .setUsable(circularSector.usable)
        .setDeleted(circularSector.deleted)
        .setValid(circularSector.valid)
        .setArea(circularSector.area)
        .setPerimeter(circularSector.perimeter);
}

export function buildPreviewSectorShapeProto(circularSector: PreviewSectorShape): PreviewSectorShapeProto {
    return new PreviewSectorShapeProto()
        .setRenderProp(circularSector.renderProp.toRenderPropProto())
        .setRelIndex(circularSector.relIndex)
        .setElType(circularSector.elType)
        .setName(circularSector.name)
        .setCenterPoint(buildCoordsProto(circularSector.centerPoint))
        .setStartPoint(buildCoordsProto(circularSector.startPoint))
        .setEndPoint(buildCoordsProto(circularSector.endPoint))
        .setArea(circularSector.area)
        .setPerimeter(circularSector.perimeter)
        .setValid(circularSector.valid)
        .setUsable(circularSector.usable)
        .setUnselectable(circularSector.unselectable);
}

export function buildRenderSectorProto(arc: RenderSector): RenderSectorProto {
    return new RenderSectorProto()
        .setRenderProp(arc.renderProp.toRenderPropProto())
        .setName(arc.name)
        .setRelIndex(arc.relIndex)
        .setElType(arc.elType)
        .setVertexRelIdxesList(arc.vertexRelIdxes)
        .setCenterPointIdx(arc.centerPointIdx)
        .setStartPointIdx(!isEmpty(arc.startPointIdx) ? new In32Wrapper().setValue(arc.startPointIdx) : undefined)
        .setEndPointIdx(!isEmpty(arc.endPointIdx) ? new In32Wrapper().setValue(arc.endPointIdx) : undefined)
        .setRadius(arc.radius)
        .setUsable(arc.usable)
        .setDeleted(arc.deleted)
        .setValid(arc.valid)
        .setArea(arc.area)
        .setPerimeter(arc.perimeter);
}

export function convertProtoToRenderSector(proto: RenderSectorProto): RenderSector {
    const arc = new RenderSector(Object.assign(new SectorGeoRenderProp(), proto.getRenderProp().toObject()));
    arc.name = proto.getName();
    arc.relIndex = proto.getRelIndex();
    arc.elType = proto.getElType() as GeoObjectType;
    arc.vertexRelIdxes = proto.getVertexRelIdxesList();
    arc.centerPointIdx = proto.getCenterPointIdx();
    arc.startPointIdx = proto.hasStartPointIdx() ? proto.getStartPointIdx().getValue() : undefined;
    arc.endPointIdx = proto.getEndPointIdx() ? proto.getEndPointIdx().getValue() : undefined;
    arc.radius = proto.getRadius();
    arc.usable = proto.getUsable();
    arc.deleted = proto.getDeleted();
    arc.valid = proto.getValid();
    arc.area = proto.getArea();
    arc.perimeter = proto.getPerimeter();
    return arc;
}

export function buildRenderEllipseProto(ellipse: RenderEllipse): RenderEllipseProto {
    return new RenderEllipseProto()
        .setRenderProp(ellipse.renderProp.toRenderPropProto())
        .setRelIndex(ellipse.relIndex)
        .setName(ellipse.name)
        .setF1Idx(ellipse.f1Idx)
        .setF2Idx(ellipse.f2Idx)
        .setA(ellipse.a)
        .setB(ellipse.b)
        .setRotate(ellipse.rotate)
        .setUsable(ellipse.usable)
        .setDeleted(ellipse.deleted)
        .setValid(ellipse.valid)
        .setArea(ellipse.area)
        .setPerimeter(ellipse.perimeter);
}

export function convertProtoToRenderEllipse(proto: RenderEllipseProto): RenderEllipse {
    const ellipse = new RenderEllipse(Object.assign(new EllipseGeoRenderProp(), proto.getRenderProp().toObject()));
    ellipse.name = proto.getName();
    ellipse.relIndex = proto.getRelIndex();
    ellipse.f1Idx = proto.getF1Idx();
    ellipse.f2Idx = proto.getF2Idx();
    ellipse.a = proto.getA();
    ellipse.b = proto.getB();
    ellipse.rotate = proto.getRotate();
    ellipse.usable = proto.getUsable();
    ellipse.deleted = proto.getDeleted();
    ellipse.valid = proto.getValid();
    ellipse.area = proto.getArea();
    ellipse.perimeter = proto.getPerimeter();
    return ellipse;
}

export function buildRenderCircleProto(arc: RenderCircle): RenderCircleProto {
    return new RenderCircleProto()
        .setRenderProp(arc.renderProp.toRenderPropProto())
        .setName(arc.name)
        .setRelIndex(arc.relIndex)
        .setElType(arc.elType)
        .setVertexRelIdxesList(arc.vertexRelIdxes)
        .setCenterPointIdx(arc.centerPointIdx)
        .setEndPointIdx(!isEmpty(arc.endPointIdx) ? new In32Wrapper().setValue(arc.endPointIdx) : undefined)
        .setRadius(arc.radius)
        .setUsable(arc.usable)
        .setDeleted(arc.deleted)
        .setValid(arc.valid)
        .setArea(arc.area)
        .setPerimeter(arc.perimeter);
}

export function convertProtoToRenderCircle(proto: RenderCircleProto): RenderCircle {
    const arc = new RenderCircle(Object.assign(new CircleGeoRenderProp(), proto.getRenderProp().toObject()));
    arc.name = proto.getName();
    arc.relIndex = proto.getRelIndex();
    arc.elType = proto.getElType() as GeoObjectType;
    arc.vertexRelIdxes = proto.getVertexRelIdxesList();
    arc.centerPointIdx = proto.getCenterPointIdx();
    arc.endPointIdx = proto.getEndPointIdx() ? proto.getEndPointIdx().getValue() : undefined;
    arc.radius = proto.getRadius();
    arc.usable = proto.getUsable();
    arc.deleted = proto.getDeleted();
    arc.valid = proto.getValid();
    arc.area = proto.getArea();
    arc.perimeter = proto.getPerimeter();
    return arc;
}

export function convertProtoToRenderSectorShape(proto: RenderSectorShapeProto): RenderSectorShape {
    const sector = new RenderSectorShape(
        Object.assign(new SectorShapeGeoRenderProp(), proto.getRenderProp().toObject())
    );
    sector.name = proto.getName();
    sector.relIndex = proto.getRelIndex();
    sector.elType = proto.getElType() as GeoObjectType;
    sector.vertexRelIdxes = proto.getVertexRelIdxesList();
    sector.lineRelIdxes = proto.getLineRelIdxesList();
    sector.arcRelIdx = proto.getArcIdx();
    sector.centerPointIdx = proto.getCenterPointIdx();
    sector.startPointIdx = proto.getStartPointIdx();
    sector.endPointIdx = proto.getEndPointIdx();
    sector.radius = proto.getRadius();
    sector.usable = proto.getUsable();
    sector.deleted = proto.getDeleted();
    sector.valid = proto.getValid();
    sector.area = proto.getArea();
    sector.perimeter = proto.getPerimeter();
    return sector;
}

export function convertProtoToPreviewSectorShape(proto: PreviewSectorShapeProto): PreviewSectorShape {
    const sector = new PreviewSectorShape(
        Object.assign(new SectorShapeGeoRenderProp(), proto.getRenderProp().toObject())
    );
    sector.relIndex = proto.getRelIndex();
    sector.name = proto.getName();
    sector.elType = proto.getElType() as GeoObjectType;
    sector.centerPoint = convertProtoToCoords(proto.getCenterPoint());
    sector.startPoint = convertProtoToCoords(proto.getStartPoint());
    sector.endPoint = convertProtoToCoords(proto.getEndPoint());
    sector.area = proto.getArea();
    sector.perimeter = proto.getPerimeter();
    sector.valid = proto.getValid();
    sector.usable = proto.getUsable();
    sector.unselectable = proto.getUnselectable();
    return sector;
}

export function convertProtoToRenderEllipseShape(proto: RenderEllipseShapeProto): RenderEllipseShape {
    const ellipse = new RenderEllipseShape(
        Object.assign(new CircleShapeGeoRenderProp(), proto.getRenderProp().toObject())
    );
    ellipse.name = proto.getName();
    ellipse.relIndex = proto.getRelIndex();
    ellipse.arcRelIdx = proto.getArcIdx();
    ellipse.f1Idx = proto.getF1Idx();
    ellipse.f2Idx = proto.getF2Idx();
    ellipse.a = proto.getA();
    ellipse.b = proto.getB();
    ellipse.rotate = proto.getRotate();
    ellipse.usable = proto.getUsable();
    ellipse.deleted = proto.getDeleted();
    ellipse.valid = proto.getValid();
    ellipse.area = proto.getArea();
    ellipse.perimeter = proto.getPerimeter();
    return ellipse;
}

export function convertProtoToPreviewEllipseShape(proto: PreviewEllipseShapeProto): PreviewEllipseShape {
    const ellipse = new PreviewEllipseShape(
        Object.assign(new EllipseShapeGeoRenderProp(), proto.getRenderProp().toObject())
    );
    ellipse.relIndex = proto.getRelIndex();
    ellipse.name = proto.getName();
    ellipse.a = proto.getA();
    ellipse.b = proto.getB();
    ellipse.f1 = convertProtoToCoords(proto.getF1());
    ellipse.f2 = convertProtoToCoords(proto.getF2());
    ellipse.rotate = proto.getRotate();
    ellipse.area = proto.getArea();
    ellipse.perimeter = proto.getPerimeter();
    ellipse.usable = proto.getUsable();
    ellipse.valid = proto.getValid();
    ellipse.unselectable = proto.getUnselectable();
    return ellipse;
}

export function buildRenderEllipseShapeProto(ellipse: RenderEllipseShape): RenderEllipseShapeProto {
    return new RenderEllipseShapeProto()
        .setRenderProp(ellipse.renderProp.toRenderPropProto())
        .setRelIndex(ellipse.relIndex)
        .setArcIdx(ellipse.arcRelIdx)
        .setName(ellipse.name)
        .setF1Idx(ellipse.f1Idx)
        .setF2Idx(ellipse.f2Idx)
        .setA(ellipse.a)
        .setB(ellipse.b)
        .setRotate(ellipse.rotate)
        .setUsable(ellipse.usable)
        .setDeleted(ellipse.deleted)
        .setValid(ellipse.valid)
        .setArea(ellipse.area)
        .setPerimeter(ellipse.perimeter);
}

export function buildPreviewEllipseShapeProto(ellipse: PreviewEllipseShape): PreviewEllipseShapeProto {
    return new PreviewEllipseShapeProto()
        .setRenderProp(ellipse.renderProp.toRenderPropProto())
        .setRelIndex(ellipse.relIndex)
        .setName(ellipse.name)
        .setF1(buildCoordsProto(ellipse.f1))
        .setF2(buildCoordsProto(ellipse.f2))
        .setA(ellipse.a)
        .setB(ellipse.b)
        .setRotate(ellipse.rotate)
        .setArea(ellipse.area)
        .setPerimeter(ellipse.perimeter)
        .setValid(ellipse.valid)
        .setUsable(ellipse.usable)
        .setUnselectable(ellipse.unselectable);
}

export function buildRenderPolygonProto(polygon: RenderPolygon): RenderPolygonProto {
    return new RenderPolygonProto()
        .setRenderProp(polygon.renderProp.toRenderPropProto())
        .setRelIndex(polygon.relIndex)
        .setElType(polygon.elType)
        .setVertexRelIdxesList(polygon.vertexRelIdxes)
        .setLineRelIdxesList(polygon.lineRelIdxes)
        .setName(polygon.name)
        .setFacesList(polygon.faces)
        .setUsable(polygon.usable)
        .setDeleted(polygon.deleted)
        .setValid(polygon.valid)
        .setArea(polygon.area)
        .setPerimeter(polygon.perimeter);
}

export function buildPreviewPolygonProto(polygon: PreviewPolygon): PreviewPolygonProto {
    return new PreviewPolygonProto()
        .setRenderProp(polygon.renderProp.toRenderPropProto())
        .setRelIndex(polygon.relIndex)
        .setElType(polygon.elType)
        .setName(polygon.name)
        .setFacesList(polygon.faces.map(f => buildCoordsProto(f)))
        .setArea(polygon.area)
        .setPerimeter(polygon.perimeter)
        .setValid(polygon.valid)
        .setUsable(polygon.usable)
        .setUnselectable(polygon.unselectable);
}

export function convertProtoToRenderPolygon(proto: RenderPolygonProto): RenderPolygon {
    const polygon = new RenderPolygon(Object.assign(new PolygonGeoRenderProp(), proto.getRenderProp().toObject()));
    polygon.relIndex = proto.getRelIndex();
    polygon.elType = proto.getElType() as GeoObjectType;
    polygon.vertexRelIdxes = proto.getVertexRelIdxesList();
    polygon.lineRelIdxes = proto.getLineRelIdxesList();
    polygon.name = proto.getName();
    polygon.faces = proto.getFacesList();
    polygon.usable = proto.getUsable();
    polygon.deleted = proto.getDeleted();
    polygon.valid = proto.getValid();
    polygon.area = proto.getArea();
    polygon.perimeter = proto.getPerimeter();
    return polygon;
}

export function convertProtoToPreviewPolygon(proto: PreviewPolygonProto): PreviewPolygon {
    const polygon = new PreviewPolygon(Object.assign(new PolygonGeoRenderProp(), proto.getRenderProp().toObject()));
    polygon.relIndex = proto.getRelIndex();
    polygon.elType = proto.getElType() as GeoObjectType;
    polygon.name = proto.getName();
    polygon.faces = proto.getFacesList().map(f => convertProtoToCoords(f));
    polygon.area = proto.getArea();
    polygon.perimeter = proto.getPerimeter();
    polygon.valid = proto.getValid();
    polygon.usable = proto.getUsable();
    polygon.unselectable = proto.getUnselectable();
    return polygon;
}

export function convertProtoToDocRenderProp(proto: DocRenderPropProto): DocRenderProp {
    const docRenderProp = <DocRenderProp>{};

    if (proto.hasScreenUnit()) docRenderProp.screenUnit = proto.getScreenUnit();
    if (proto.hasCanvasWidth()) docRenderProp.canvasWidth = proto.getCanvasWidth();
    if (proto.hasCanvasHeight()) docRenderProp.canvasHeight = proto.getCanvasHeight();
    if (proto.hasScale()) docRenderProp.scale = proto.getScale();
    if (proto.getTranslationList().length) docRenderProp.translation = proto.getTranslationList();
    else docRenderProp.translation = [0, 0, 0];
    if (proto.getRotationList().length) docRenderProp.rotation = proto.getRotationList();
    else docRenderProp.rotation = [0, 0, 0];
    if (proto.hasValid()) docRenderProp.valid = proto.getValid();
    if (proto.hasShadow()) docRenderProp.shadow = proto.getShadow();
    if (proto.hasShadowStyle()) docRenderProp.shadowStyle = proto.getShadowStyle();
    if (proto.hasAxis()) docRenderProp.axis = proto.getAxis();
    if (proto.hasGrid()) docRenderProp.grid = proto.getGrid();
    if (proto.hasDetailGrid()) docRenderProp.detailGrid = proto.getDetailGrid();
    if (proto.hasBorder()) docRenderProp.border = proto.getBorder();
    if (proto.hasBorderStyle()) docRenderProp.borderStyle = proto.getBorderStyle();
    if (proto.hasBorderColor()) docRenderProp.borderColor = proto.getBorderColor();
    if (proto.hasSnapMode()) docRenderProp.snapMode = proto.getSnapMode();
    if (proto.hasSnapToExistingPoints()) docRenderProp.snapToExistingPoints = proto.getSnapToExistingPoints();
    if (proto.hasSnapToGrid()) docRenderProp.snapToGrid = proto.getSnapToGrid();
    if (proto.hasNamingMode()) docRenderProp.namingMode = proto.getNamingMode();

    return docRenderProp;
}

export function convertDocRenderPropToProto(docRenderProp: DocRenderProp): DocRenderPropProto {
    const proto = new DocRenderPropProto();

    if (docRenderProp.screenUnit !== undefined) proto.setScreenUnit(docRenderProp.screenUnit);
    if (docRenderProp.canvasWidth !== undefined) proto.setCanvasWidth(docRenderProp.canvasWidth);
    if (docRenderProp.canvasHeight !== undefined) proto.setCanvasHeight(docRenderProp.canvasHeight);
    if (docRenderProp.scale !== undefined) proto.setScale(docRenderProp.scale);
    if (docRenderProp.translation !== undefined) proto.setTranslationList(docRenderProp.translation);
    if (docRenderProp.rotation !== undefined) proto.setRotationList(docRenderProp.rotation);
    if (docRenderProp.valid !== undefined) proto.setValid(docRenderProp.valid);
    if (docRenderProp.shadow !== undefined) proto.setShadow(docRenderProp.shadow);
    if (docRenderProp.shadowStyle !== undefined) proto.setShadowStyle(docRenderProp.shadowStyle);
    if (docRenderProp.axis !== undefined) proto.setAxis(docRenderProp.axis);
    if (docRenderProp.grid !== undefined) proto.setGrid(docRenderProp.grid);
    if (docRenderProp.detailGrid !== undefined) proto.setDetailGrid(docRenderProp.detailGrid);
    if (docRenderProp.border !== undefined) proto.setBorder(docRenderProp.border);
    if (docRenderProp.borderStyle !== undefined) proto.setBorderStyle(docRenderProp.borderStyle);
    if (docRenderProp.borderColor !== undefined) proto.setBorderColor(docRenderProp.borderColor);
    if (docRenderProp.snapMode !== undefined) proto.setSnapMode(docRenderProp.snapMode);
    if (docRenderProp.snapToExistingPoints !== undefined)
        proto.setSnapToExistingPoints(docRenderProp.snapToExistingPoints);
    if (docRenderProp.snapToGrid !== undefined) proto.setSnapToGrid(docRenderProp.snapToGrid);
    if (docRenderProp.namingMode !== undefined) proto.setNamingMode(docRenderProp.namingMode);

    return proto;
}

export function convertProtoToDocDefaultElRenderProps(proto: DocDefaultElRenderPropsProto): DefaultGeoRenderProp {
    const dderp = new DefaultGeoRenderProp(); // docDefaultElRenderProps
    if (proto.hasColor()) dderp.color = proto.getColor();
    if (proto.hasPointColor()) dderp.pointColor = proto.getPointColor();
    if (proto.hasLineColor()) dderp.lineColor = proto.getLineColor();
    if (proto.hasStrokeStyle()) dderp.strokeStyle = proto.getStrokeStyle() as GeoStrokeStyle;
    if (proto.hasLineWeight()) dderp.lineWeight = proto.getLineWeight();
    if (proto.hasOpacity()) dderp.opacity = proto.getOpacity();
    if (proto.hasSpaceFromArcToCorner()) dderp.spaceFromArcToCorner = proto.getSpaceFromArcToCorner();
    return dderp;
}

export function convertDocDefaultElRenderPropsToProto(
    docDefaultElRenderProps: DefaultGeoRenderProp
): DocDefaultElRenderPropsProto {
    const proto = new DocDefaultElRenderPropsProto();
    if (docDefaultElRenderProps.pointColor !== undefined) proto.setPointColor(docDefaultElRenderProps.pointColor);
    if (docDefaultElRenderProps.color !== undefined) proto.setColor(docDefaultElRenderProps.color);
    if (docDefaultElRenderProps.lineColor !== undefined) proto.setLineColor(docDefaultElRenderProps.lineColor);
    if (docDefaultElRenderProps.strokeStyle !== undefined)
        proto.setStrokeStyle(docDefaultElRenderProps.strokeStyle as GeoStrokeStyle);
    if (docDefaultElRenderProps.lineWeight !== undefined) proto.setLineWeight(docDefaultElRenderProps.lineWeight);
    if (docDefaultElRenderProps.opacity !== undefined) proto.setOpacity(docDefaultElRenderProps.opacity);
    if (docDefaultElRenderProps.spaceFromArcToCorner !== undefined)
        proto.setSpaceFromArcToCorner(docDefaultElRenderProps.spaceFromArcToCorner);
    return proto;
}
