import {
    AngleGeoRenderProp,
    CircleGeoRenderProp,
    CircleShapeGeoRenderProp,
    EllipseGeoRenderProp,
    GeoPreviewElement,
    GeoRenderElement,
    LineGeoRenderProp,
    LineSegmentGeoRenderProp,
    PointGeoRenderProp,
    PolygonGeoRenderProp,
    PreviewAngle,
    PreviewCircleShape,
    PreviewEllipseShape,
    PreviewLine,
    PreviewPolygon,
    PreviewSectorShape,
    PreviewVertex,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
    SectorGeoRenderProp,
    SectorShapeGeoRenderProp,
} from '../model';
import { GeoDocCtrl } from '../objects';
import { cmdMeta, CriticalErr, reliableCmdMeta, ViErr } from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.geo';
import {
    EndPreviewCmd,
    PreviewAngleByThreePointsCmd,
    PreviewAngleCmd,
    PreviewCircleShapeCmd,
    PreviewEllipseShapeCmd,
    PreviewLineCmd,
    PreviewPolygonCmd,
    PreviewSectorShapeCmd,
    PreviewVertexCmd,
    RenderAngleCmd,
    RenderCircleCmd,
    RenderCircleShapeCmd,
    RenderEllipseCmd,
    RenderEllipseShapeCmd,
    RenderLineCmd,
    RenderLineSegmentCmd,
    RenderPolygonCmd,
    RenderSectorCmd,
    RenderSectorShapeCmd,
    RenderVertexCmd,
    UpdateDocStateCmd,
} from './geo.cmd';
import { buildRenderElementFromObject } from '../model/render.element.utils';
import { convertDocRenderPropToProto } from './proto.utils';

/**
 * Synchronizes rendering commands for a given set of GeoRenderElement objects.
 * This function processes each render element, creates the corresponding command, and sends it to the command channel.
 *
 * @param renders An array of GeoRenderElement objects to be processed.
 * @param docCtrl The document controller managing the rendering operations.
 * @throws {ViErr} If a known error occurs during synchronization.
 * @throws {CriticalErr} If an unexpected error occurs, prompting the user to reload the page.
 */
export async function syncRenderCommands(renders: GeoRenderElement[], docCtrl: GeoDocCtrl) {
    try {
        const layerCtrl = docCtrl.layers[0];

        for (const gre of renders) {
            let cmd = null;
            switch (gre.type) {
                case 'RenderVertex': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_VERTEX);
                    cmd = new RenderVertexCmd(meta);
                    cmd.setVertex(buildRenderElementFromObject(gre as RenderVertex, RenderVertex, PointGeoRenderProp));
                    break;
                }
                case 'RenderLineSegment': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_LINE_SEGMENT
                    );
                    cmd = new RenderLineSegmentCmd(meta);
                    cmd.setLine(
                        buildRenderElementFromObject(
                            gre as RenderLineSegment,
                            RenderLineSegment,
                            LineSegmentGeoRenderProp
                        )
                    );
                    break;
                }
                case 'RenderVector':
                case 'RenderRay':
                case 'RenderLine': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_LINE);
                    cmd = new RenderLineCmd(meta);
                    cmd.setLine(buildRenderElementFromObject(gre as RenderLine, RenderLine, LineGeoRenderProp));
                    break;
                }
                case 'RenderPolygon': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_POLYGON
                    );
                    cmd = new RenderPolygonCmd(meta);
                    cmd.setPolygon(
                        buildRenderElementFromObject(gre as RenderPolygon, RenderPolygon, PolygonGeoRenderProp)
                    );
                    break;
                }
                case 'RenderCircleShape': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_CIRCLE_SHAPE
                    );
                    cmd = new RenderCircleShapeCmd(meta);
                    cmd.setCircle(
                        buildRenderElementFromObject(
                            gre as RenderCircleShape,
                            RenderCircleShape,
                            CircleShapeGeoRenderProp
                        )
                    );
                    break;
                }
                case 'RenderEllipseShape': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_ELLIPSE_SHAPE
                    );
                    cmd = new RenderEllipseShapeCmd(meta);
                    cmd.setEllipse(
                        buildRenderElementFromObject(
                            gre as RenderEllipseShape,
                            RenderEllipseShape,
                            CircleShapeGeoRenderProp
                        )
                    );
                    break;
                }
                case 'RenderSectorShape': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_CIRCULAR_SECTOR_SHAPE
                    );
                    cmd = new RenderSectorShapeCmd(meta);
                    cmd.setSector(
                        buildRenderElementFromObject(
                            gre as RenderSectorShape,
                            RenderSectorShape,
                            SectorShapeGeoRenderProp
                        )
                    );
                    break;
                }
                case 'RenderAngle': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_ANGLE);
                    cmd = new RenderAngleCmd(meta);
                    cmd.setAngle(buildRenderElementFromObject(gre as RenderAngle, RenderAngle, AngleGeoRenderProp));
                    break;
                }
                case 'RenderSector': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_SECTOR);
                    cmd = new RenderSectorCmd(meta);
                    cmd.setArc(buildRenderElementFromObject(gre as RenderSector, RenderSector, SectorGeoRenderProp));
                    break;
                }
                case 'RenderEllipse': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_ELLIPSE
                    );
                    cmd = new RenderEllipseCmd(meta);
                    cmd.setArc(buildRenderElementFromObject(gre as RenderEllipse, RenderEllipse, EllipseGeoRenderProp));
                    break;
                }
                case 'RenderCircle': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_CIRCLE);
                    cmd = new RenderCircleCmd(meta);
                    cmd.setArc(buildRenderElementFromObject(gre as RenderCircle, RenderCircle, CircleGeoRenderProp));
                    break;
                }
                default:
                    break;
            }

            if (cmd) {
                cmd.setLayer(layerCtrl.state.id);
                await docCtrl.editor.cmdChannel.receive(cmd);
            }
        }
    } catch (e) {
        if (e instanceof ViErr) throw e;
        throw new CriticalErr('Có lỗi xảy ra khi đồng bộ hiển thị. Vui lòng tải lại trang', e);
    }
}

export async function syncUpdateDocStateCommand(docCtrl: GeoDocCtrl) {
    const doc = docCtrl.state;
    const meta = reliableCmdMeta(docCtrl.viewport, doc.id, -1, CmdTypeProto.UPDATE_DOC_STATE);
    const cmd = new UpdateDocStateCmd(meta);
    cmd.setState(doc.globalId, convertDocRenderPropToProto(doc.docRenderProp));
    await docCtrl.editor.cmdChannel.receive(cmd);
}

export async function syncEndPreviewModeCommand(docCtrl: GeoDocCtrl) {
    const layerCtrl = docCtrl.layers[0];
    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, -1, CmdTypeProto.END_PREVIEW);
    const cmd = new EndPreviewCmd(meta);
    cmd.setLayer(layerCtrl.state.id);
    await docCtrl.editor.cmdChannel.receive(cmd);
}

/**
 * Synchronizes preview rendering commands for a given GeoPreviewElement.
 * This function determines the appropriate command type based on the preview element and executes it through the command channel.
 *
 * @param preview The GeoPreviewElement object to be processed.
 * @param docCtrl The document controller managing the rendering operations.
 * @param cmdType (Optional) The specific command type to be executed.
 * @throws {ViErr} If a known error occurs during synchronization.
 * @throws {CriticalErr} If an unexpected error occurs, prompting the user to reload the page.
 */
export async function syncPreviewCommands(preview: GeoPreviewElement, docCtrl: GeoDocCtrl, cmdType?: CmdTypeProto) {
    const layerCtrl = docCtrl.layers[0];
    let cmd = null;

    if (cmdType) {
        switch (cmdType) {
            case CmdTypeProto.PREVIEW_ANGLE_BY_THREE_POINTS: {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_ANGLE_BY_THREE_POINTS);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewAngleByThreePointsCmd(meta);
                cmd.setAngle(buildRenderElementFromObject(preview as PreviewAngle, PreviewAngle, AngleGeoRenderProp));
                break;
            }
            default:
                break;
        }
    }

    if (!cmd) {
        switch (preview.type) {
            case 'RenderVertex': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_VERTEX);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewVertexCmd(meta);
                cmd.setVertex(
                    buildRenderElementFromObject(preview as PreviewVertex, PreviewVertex, PointGeoRenderProp)
                );
                break;
            }
            case 'RenderLineSegment':
            case 'RenderVector':
            case 'RenderRay':
            case 'RenderLine': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_LINE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewLineCmd(meta);
                cmd.setLine(buildRenderElementFromObject(preview as PreviewLine, PreviewLine, LineGeoRenderProp));
                break;
            }
            case 'RenderPolygon': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_POLYGON);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewPolygonCmd(meta);
                cmd.setPolygon(
                    buildRenderElementFromObject(preview as PreviewPolygon, PreviewPolygon, PolygonGeoRenderProp)
                );
                break;
            }
            case 'RenderCircleShape': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_CIRCLE_SHAPE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewCircleShapeCmd(meta);
                cmd.setCircle(
                    buildRenderElementFromObject(
                        preview as PreviewCircleShape,
                        PreviewCircleShape,
                        CircleShapeGeoRenderProp
                    )
                );
                break;
            }
            case 'RenderEllipseShape': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_ELLIPSE_SHAPE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewEllipseShapeCmd(meta);
                cmd.setEllipse(
                    buildRenderElementFromObject(
                        preview as PreviewEllipseShape,
                        PreviewEllipseShape,
                        CircleShapeGeoRenderProp
                    )
                );
                break;
            }
            case 'RenderSectorShape': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_CIRCULAR_SECTOR_SHAPE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewSectorShapeCmd(meta);
                cmd.setSector(
                    buildRenderElementFromObject(
                        preview as PreviewSectorShape,
                        PreviewSectorShape,
                        SectorShapeGeoRenderProp
                    )
                );
                break;
            }
            case 'RenderAngle': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_ANGLE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewAngleCmd(meta);
                cmd.setAngle(buildRenderElementFromObject(preview as PreviewAngle, PreviewAngle, AngleGeoRenderProp));
                break;
            }

            default:
                break;
        }
    }

    if (cmd) {
        cmd.setLayer(layerCtrl.state.id);
        await docCtrl.editor.cmdChannel.receive(cmd);
    }
}
