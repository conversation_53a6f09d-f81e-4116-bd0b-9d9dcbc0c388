import Flatten from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
    Position,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.geo';
import { syncPreviewCommands, syncRenderCommands } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewAngle,
    PreviewLine,
    RenderLine,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';

import { geoDefaultHandlerFn } from '../error-handler';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewAngleRenderProp,
    buildPreviewLineSegmentRenderProp,
    buildPreviewPointRenderProp,
    createVector,
    defaultNonUIPointerEventHandler,
    handleIfPointerNotInError,
    isElementLine,
    pickPointName,
} from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;
import vector = Flatten.vector;

/**
 *
 * <AUTHOR>
 */
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleByThreePointsTool';

    private points: RenderVertex[] = [];
    private lineStart: RenderLine;
    private lineEnd: RenderLine;
    private lineStartDirection: number = 1;
    private lineEndDirection: number = 1;
    private isPointerDown = false;
    private clickCount = 0;

    protected override readonly filterElementFunc = (el: GeoRenderElement) =>
        el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

            // move point/line preview
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.lineStart = undefined;
        this.lineEnd = undefined;
        this.lineStartDirection = 1;
        this.lineEndDirection = 1;
        this.isPointerDown = false;
        this.clickCount = 0;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;
        this.clickCount++;

        if (this.clickCount == 1) {
            await this.onFirstPointerDown(event);
        } else if (this.clickCount == 2) {
            await this.onSecondPointerDown(event);
        } else if (this.clickCount == 3) {
            await this.onThirdPointerDown(event);
        } else if (this.clickCount == 4) {
            await this.onFourthPointerDown(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        if (this.clickCount == 4) {
            await this.finalizeAngle(event);
        }
    }

    private generatePointAndSyncPreview(ctrl: GeoDocCtrl, pos: Position, relIndex: number): RenderVertex {
        const p: RenderVertex = {
            relIndex: relIndex,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewPointRenderProp(),
            name: undefined,
            coords: [pos.x, pos.y],
            usable: true,
            valid: true,
        };

        syncPreviewCommands(p, ctrl);

        return p;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFirstPointerDown(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);

        if (hitEl) {
            this.editor.selectElement(hitCtx);
            if (hitEl.type == 'RenderVertex') {
                const e = hitEl as RenderVertex;
                this.points[0] = e;
            } else if (isElementLine(hitEl)) {
                this.lineStart = hitEl as RenderLine;
            } else return;
        } else {
            this.points[0] = this.generatePointAndSyncPreview(ctrl, pos, -10);
            this.started = true;
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onSecondPointerDown(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);

        if (hitEl?.type == 'RenderVertex') {
            this.editor.selectElement(hitCtx);
            this.points[1] = hitEl as RenderVertex;
        } else {
            this.points[1] = this.generatePointAndSyncPreview(ctrl, pos, -11);
        }

        const p1 = this.points[0];
        const p2 = this.points[1];

        const n2 = `${p2.name}${p1.name}`;

        const calculationVector = createVector(p1, p2);
        const previewVector = [p1.coords[0] - p2.coords[0], p1.coords[1] - p2.coords[1], 0.0];
        if (previewVector[0] != calculationVector[0] || previewVector[1] != calculationVector[1])
            this.lineStartDirection = -1;

        this.lineStart = {
            relIndex: undefined,
            renderProp: buildPreviewLineSegmentRenderProp(),
            name: n2,
            type: 'RenderLineSegment',
            elType: 'LineSegment',
            startPointIdx: this.points[1].relIndex,
            endPointIdx: this.points[0].relIndex,
            vector: previewVector,
            usable: true,
            valid: true,
        };
        const line: PreviewLine = {
            relIndex: -20,
            renderProp: buildPreviewLineSegmentRenderProp(),
            name: n2,
            type: 'RenderLineSegment',
            elType: 'LineSegment',
            startPoint: this.points[1].coords,
            endPoint: this.points[0].coords,
            usable: true,
            valid: true,
        };
        syncPreviewCommands(line, ctrl);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onThirdPointerDown(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);

        if (hitEl?.type == 'RenderVertex') {
            this.editor.selectElement(hitCtx);
            const e = hitEl as RenderVertex;
            this.points[2] = e;
        } else {
            this.points[2] = this.generatePointAndSyncPreview(ctrl, pos, -13);
        }

        const p1 = this.points[1];
        const p2 = this.points[2];
        const n1 = `${p1.name}${p2.name}`;

        const calculationVector = createVector(p1, p2);
        const previewVector = [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1], 0.0];

        if (previewVector[0] != calculationVector[0] || previewVector[1] != calculationVector[1])
            this.lineEndDirection = -1;

        this.lineEnd = {
            relIndex: undefined,
            renderProp: buildPreviewLineSegmentRenderProp(),
            name: n1,
            type: 'RenderLineSegment',
            elType: 'LineSegment',
            startPointIdx: p1.relIndex,
            endPointIdx: p2.relIndex,
            vector: previewVector,
            usable: true,
            valid: true,
        };
        const line: PreviewLine = {
            relIndex: -21,
            renderProp: buildPreviewLineSegmentRenderProp(),
            name: n1,
            type: 'RenderLineSegment',
            elType: 'LineSegment',
            startPoint: p1.coords,
            endPoint: p2.coords,
            usable: true,
            valid: true,
        };
        syncPreviewCommands(line, ctrl);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFourthPointerDown(event: GeoPointerEvent) {
        // This event is just to initiate the selection of angle direction
        // Actual angle creation happens in finalizeAngle
        const { ctrl, pos } = this.posAndCtrl(event);

        // Preview the angle based on current pointer position
        this.previewAngle(ctrl, [pos.x, pos.y, 0.0]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeAngle(event: GeoPointerEvent) {
        const { ctrl, pos } = this.posAndCtrl(event);
        const v = [pos.x, pos.y, 0.0];

        // submit construction
        const constructionPoints: GeoElConstructionRequest[] = [];
        const inputPointNames = (
            await this.requestElementNames(ctrl, [
                {
                    objName: 'Angle Point',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        let lineStartName = this.lineStart.name;
        let lineEndName = this.lineEnd.name;

        let pointStart = this.points[0].name;
        const pointMiddle = this.points[1].name;
        let pointEnd = this.points[2].name;

        const pS = ctrl.rendererCtrl.elementAt(this.lineStart.startPointIdx) as RenderVertex;
        const pE = ctrl.rendererCtrl.elementAt(this.lineEnd.startPointIdx) as RenderVertex;
        const vS = this.lineStart.vector;
        const vE = this.lineEnd.vector;
        const lineS = line(point(pS.coords[0], pS.coords[1]), vector([-vS[1], vS[0]]));
        const lineE = line(point(pE.coords[0], pE.coords[1]), vector([-vE[1], vE[0]]));
        const i = lineS.intersect(lineE)[0];
        if (!i) return;
        const vC = [i.x, i.y];

        const vectorS = vector(vS[0], vS[1]);
        const vectorE = vector(vE[0], vE[1]);

        const faceS = nthDirectionByLine([vectorS.x, vectorS.y], vC, v);
        const faceE = nthDirectionByLine([vectorE.x, vectorE.y], vC, v);

        const swapCase1 = faceS == 2 && faceE == 1 && vectorS.angleTo(vectorE) > vectorE.angleTo(vectorS);
        const swapCase2 = (faceS != 1 || faceE != 2) && vectorS.angleTo(vectorE) < vectorE.angleTo(vectorS);

        if (swapCase1 || swapCase2) {
            const tmp = lineStartName;
            lineStartName = lineEndName;
            lineEndName = tmp;

            const tmp2 = pointStart;
            pointStart = pointEnd;
            pointEnd = tmp2;

            const temp3 = this.lineStartDirection;
            this.lineStartDirection = this.lineEndDirection;
            this.lineEndDirection = temp3;
        }

        const angleName: string = pointStart + pointMiddle + pointEnd;
        const constructionAngle = this.buildAngleConstruction(
            angleName,
            pointStart,
            pointMiddle,
            pointEnd,
            this.lineStartDirection,
            this.lineEndDirection
        );

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo góc',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionAngle,
                        },
                    ])
                );
                await syncRenderCommands(constructResponse.render, ctrl);
                this.started = false;
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length <= 0 && !this.lineStart && !this.lineEnd) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown) {
            // Update while dragging the current point
            if (this.clickCount === 1) {
                this.onFirstPointerDown(event);
            } else if (this.clickCount === 2) {
                this.onSecondPointerDown(event);
            } else if (this.clickCount === 3) {
                this.onThirdPointerDown(event);
            } else if (this.clickCount === 4) {
                this.previewAngleForCurrentPosition(event);
            }
        } else {
            // Preview based on where we are in the process
            const { ctrl, pos, hitEl } = this.posAndCtrl(event, !!this.lineStart && !!this.lineEnd);

            let pointPosition = [pos.x, pos.y, 0.0];
            if (hitEl?.type == 'RenderVertex') {
                pointPosition = (hitEl as RenderVertex).coords;
            }

            if (!this.lineStart) {
                if (this.points.length == 1) {
                    // Preview for first line (from first point to mouse)
                    const v1 = this.points[0].coords;
                    const line: PreviewLine = {
                        relIndex: -20,
                        renderProp: buildPreviewLineSegmentRenderProp(),
                        name: '',
                        type: 'RenderLineSegment',
                        elType: 'LineSegment',
                        startPoint: v1,
                        endPoint: pointPosition,
                        usable: true,
                        valid: true,
                    };
                    syncPreviewCommands(line, ctrl);
                }
            } else if (!this.lineEnd) {
                if (this.points.length == 1 || this.points.length == 2) {
                    // Preview for second line
                    const v1 = this.points[this.points.length - 1].coords;
                    const line: PreviewLine = {
                        relIndex: -21,
                        renderProp: buildPreviewLineSegmentRenderProp(),
                        name: '',
                        type: 'RenderLineSegment',
                        elType: 'LineSegment',
                        startPoint: v1,
                        endPoint: pointPosition,
                        usable: true,
                        valid: true,
                    };
                    syncPreviewCommands(line, ctrl);
                }
            } else {
                // When both lines are set, preview the angle
                this.previewAngle(ctrl, pointPosition);
            }
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private previewAngleForCurrentPosition(event: GeoPointerEvent) {
        const { ctrl, pos } = this.posAndCtrl(event);
        this.previewAngle(ctrl, [pos.x, pos.y, 0.0]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private previewAngle(ctrl: GeoDocCtrl, pointPosition: number[]) {
        if (!this.lineStart || !this.lineEnd) return;

        const pS = ctrl.rendererCtrl.elementAt(this.lineStart.startPointIdx) as RenderVertex;
        const pE = ctrl.rendererCtrl.elementAt(this.lineEnd.startPointIdx) as RenderVertex;
        let vS = this.lineStart.vector;
        let vE = this.lineEnd.vector;
        const lineS = line(point(pS.coords[0], pS.coords[1]), vector([-vS[1], vS[0]]));
        const lineE = line(point(pE.coords[0], pE.coords[1]), vector([-vE[1], vE[0]]));
        const i = lineS.intersect(lineE)[0];
        if (!i) return;
        const vC = [i.x, i.y];

        const vectorS = vector(vS[0], vS[1]);
        const vectorE = vector(vE[0], vE[1]);

        const faceS = nthDirectionByLine([vectorS.x, vectorS.y], vC, pointPosition);
        const faceE = nthDirectionByLine([vectorE.x, vectorE.y], vC, pointPosition);

        if (faceS == 2 && faceE == 1 && vectorS.angleTo(vectorE) > vectorE.angleTo(vectorS)) {
            const tmp = vS;
            vS = vE;
            vE = tmp;
        }

        if ((faceS != 1 || faceE != 2) && vectorS.angleTo(vectorE) < vectorE.angleTo(vectorS)) {
            const tmp = vS;
            vS = vE;
            vE = tmp;
        }

        const angle: PreviewAngle = {
            relIndex: -30,
            name: '',
            type: 'RenderAngle',
            elType: 'Angle',
            anglePoint: vC,
            vStart: [vS[0], vS[1]],
            vEnd: [vE[0], vE[1]],
            renderProp: buildPreviewAngleRenderProp(),
            usable: true,
            valid: true,
            degree: vectorS.angleTo(vectorE),
        };
        syncPreviewCommands(angle, ctrl, CmdTypeProto.PREVIEW_ANGLE_BY_THREE_POINTS);
    }

    private buildAngleConstruction(
        name: string,
        firstPointName: string,
        secondPointName: string,
        threePointName: string,
        startLineDirection: number,
        endLineDirection: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromThreePoints');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: firstPointName,
                    },
                },
            },

            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: secondPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: threePointName,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: [startLineDirection, endLineDirection],
                    },
                },
            },
        ];

        return construction;
    }
}
