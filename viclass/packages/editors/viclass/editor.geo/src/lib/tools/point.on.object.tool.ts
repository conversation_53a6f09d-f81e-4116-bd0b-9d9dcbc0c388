import { circle, line, Point, point, vector } from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
    Position,
} from '@viclass/editor.core';
import { syncEndPreviewModeCommand, syncPreviewCommands, syncRenderCommands } from '../cmd';
import { intersectionLineEllipse } from '../element.intersection/intersections';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    PointGeoRenderProp,
    PreviewVertex,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { sortOnLineV2 } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewPointRenderProp,
    defaultNonUIPointerEventHandler,
    handleIfPointerNotInError,
    isElementLine,
    isValidIdx,
    pickPointName,
} from './tool.utils';

type ConstructionArg = {
    docCtrl: GeoDocCtrl;
    name: string;
    numberParam: number;
    constructionType:
        | 'Point/PointOnEllipseEC'
        | 'Point/PointOnCircleEC'
        | 'Point/PointOnCircularSectorEC'
        | 'Point/PointOnLineEC';
    elType: 'Ellipse' | 'Circle' | 'CircularSector' | 'LineSegment' | 'Line';
    paramType: 'anEllipse' | 'aCircle' | 'aCircularSector' | 'aLineSegment' | 'aLine';
    paramTpl: 'tpl-OnEllipse' | 'tpl-OnCircle' | 'tpl-OnCircularSector' | 'tpl-OnLineSegment' | 'tpl-OnLine';
    numberTpl: 'tpl-AngleRadian' | 'tpl-Ratio' | 'tpl-CoefficientIs';
    numberParamName?: string; // 'value' or 'name'
    numberValueKey?: string; // default: value
};

export class PointOnObjectTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'PointOnObjectTool';

    private points: RenderVertex[] = [];
    private isPointerDown = false;
    private activeElement: GeoRenderElement | null = null;

    protected override readonly filterElementFunc = (el: GeoRenderElement): boolean => {
        return (
            el.type === 'RenderCircle' || isElementLine(el) || el.type === 'RenderSector' || el.type === 'RenderEllipse'
        );
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start selection
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            // confirm selection
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/preview
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.isPointerDown = false;
        this.activeElement = null;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown':
                this.onPointerDown(event);
                break;
            case 'pointerup':
                this.onPointerUp(event);
                break;
            case 'pointermove':
                this.onPointerMove(event);
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return;

        const { ctrl, pos, hitEl } = this.posAndCtrl(event);
        if (!hitEl) {
            syncEndPreviewModeCommand(ctrl);
            this.resetState();
            return;
        }

        this.isPointerDown = true;
        this.activeElement = hitEl;

        // Start preview on pointer down
        await this.processObject(ctrl, pos, hitEl, true);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return;

        const { ctrl, pos } = this.posAndCtrl(event);

        if (this.activeElement) {
            // Confirm the construction of the point
            await this.processObject(ctrl, pos, this.activeElement, false);
        }

        this.isPointerDown = false;
        this.activeElement = null;
    }

    private onPointerMove(event: GeoPointerEvent) {
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        const { ctrl, pos, hitEl } = this.posAndCtrl(event);

        if (this.isPointerDown) {
            // While dragging use the "active" element
            if (this.activeElement) this.processObject(ctrl, pos, this.activeElement, true);
        } else {
            // Hover mode: only show preview if not already selected or hovered
            if (!hitEl) {
                syncEndPreviewModeCommand(ctrl);
                return;
            }
            if (this.points.includes(hitEl as RenderVertex)) return;
            this.processObject(ctrl, pos, hitEl, true);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async processObject(
        docCtrl: GeoDocCtrl,
        pointerPos: Position,
        hitEl: GeoRenderElement,
        preview: boolean = true
    ): Promise<any> {
        let vM = [pointerPos.x, pointerPos.y, 0];

        if (hitEl.type === 'RenderVertex') {
            // Points - ignore for "point on object" tool for now
            return;
        }

        // Reusable preview builder
        const buildPreviewVertex = (coords: number[], relIndex: number = -10): PreviewVertex => ({
            type: 'RenderVertex',
            elType: 'Point',
            relIndex,
            coords: coords,
            name: '',
            renderProp: buildPreviewPointRenderProp(),
            usable: true,
            valid: true,
            unselectable: true,
        });

        // Reusable awareness block
        const doAwarenessConstruction = async (
            docCtrl: GeoDocCtrl,
            construction: GeoElConstructionRequest | undefined
        ) => {
            if (!construction) return;
            return docCtrl.editor.awarenessFeature.useAwareness(
                docCtrl.viewport.id,
                'Đang tạo điểm',
                buildDocumentAwarenessCmdOption(docCtrl.editor.awarenessConstructId, docCtrl),
                async () => {
                    const constructResponse = await this.editor.geoGateway.construct(docCtrl.state.globalId, [
                        {
                            construction,
                        },
                    ]);
                    await syncRenderCommands(constructResponse.render, docCtrl);
                    addHistoryItemFromConstructionResponse(docCtrl, constructResponse);
                }
            );
        };

        if (isElementLine(hitEl)) {
            const lineRel = hitEl as RenderLine;
            const p1Rel = docCtrl.rendererCtrl.elementAt(lineRel.startPointIdx) as RenderVertex;
            const p2Rel = docCtrl.rendererCtrl.elementAt(lineRel.endPointIdx) as RenderVertex;

            const pM = point(vM[0], vM[1]);
            const p1 = point(p1Rel.coords[0], p1Rel.coords[1]);

            // Find the closest point (projection) on the infinite line
            const vLine = vector(lineRel.vector[1], -lineRel.vector[0]);
            const l = line(p1, vLine);
            const i = pM.projectionOn(l);

            if (preview) {
                syncPreviewCommands(buildPreviewVertex([i.x, i.y]), docCtrl);
                return;
            }

            this.resetState();

            let construction: GeoElConstructionRequest | undefined;
            if (lineRel.type === 'RenderLineSegment' || lineRel.type === 'RenderVector') {
                const p2 = point(p2Rel.coords[0], p2Rel.coords[1]);
                const k = p1.distanceTo(i)[0] / p1.distanceTo(p2)[0];
                construction = await this.buildConstructionHelper({
                    docCtrl,
                    name: hitEl.name,
                    numberParam: k,
                    constructionType: 'Point/PointOnLineEC',
                    elType: 'LineSegment',
                    paramType: 'aLineSegment',
                    paramTpl: 'tpl-OnLineSegment',
                    numberTpl: 'tpl-Ratio',
                    numberParamName: 'value',
                });
            } else if (lineRel.type === 'RenderLine' || lineRel.type === 'RenderRay') {
                const unitVector = vector(lineRel.vector[0], lineRel.vector[1]).normalize();
                const newVector = vector(p1, i);
                const k = newVector.x / unitVector.x;
                construction = await this.buildConstructionHelper({
                    docCtrl,
                    name: hitEl.name,
                    numberParam: k,
                    constructionType: 'Point/PointOnLineEC',
                    elType: 'Line',
                    paramType: 'aLine',
                    paramTpl: 'tpl-OnLine',
                    numberTpl: 'tpl-CoefficientIs',
                    numberParamName: 'value',
                });
            }

            return doAwarenessConstruction(docCtrl, construction);
        }

        if (hitEl.type === 'RenderSector' || hitEl.type === 'RenderCircle') {
            const arcEl = hitEl as RenderSector;
            const pCRel = docCtrl.rendererCtrl.elementAt(arcEl.centerPointIdx) as RenderVertex;
            const pC = point(pCRel.coords[0], pCRel.coords[1]);
            let pM = point(vM[0], vM[1]);
            const c = circle(pC, arcEl.radius);
            const l = line(pC, pM);
            const i = c.intersect(l);
            if (i.length < 1) throw new Error('invalid circle intersection for point on object');

            if (pM.distanceTo(i[0])[0] < pM.distanceTo(i[1])[0]) {
                vM = [i[0].x, i[0].y, 0];
            } else {
                vM = [i[1].x, i[1].y, 0];
            }
            pM = point(vM[0], vM[1]);
            const vecCM = vector(pC, pM);
            const vecC0 = vector(pC, point(pC.x + 10, pC.y));

            if (preview) {
                syncPreviewCommands(buildPreviewVertex(vM), docCtrl);
                return;
            }

            this.resetState();

            let construction: GeoElConstructionRequest | undefined;
            if (isValidIdx(arcEl.startPointIdx) && isValidIdx(arcEl.endPointIdx)) {
                const pSRel = docCtrl.rendererCtrl.elementAt(arcEl.startPointIdx) as RenderVertex;
                const pS = point(pSRel.coords[0], pSRel.coords[1]);
                const vecCS = vector(pC, pS);
                const angle = vecCS.angleTo(vecCM);
                construction = await this.buildConstructionHelper({
                    docCtrl,
                    name: hitEl.name,
                    numberParam: angle,
                    constructionType: 'Point/PointOnCircularSectorEC',
                    elType: 'CircularSector',
                    paramType: 'aCircularSector',
                    paramTpl: 'tpl-OnCircularSector',
                    numberTpl: 'tpl-AngleRadian',
                    numberParamName: 'value',
                });
            } else {
                const angle = vecC0.angleTo(vecCM);
                construction = await this.buildConstructionHelper({
                    docCtrl,
                    name: hitEl.name,
                    numberParam: angle,
                    constructionType: 'Point/PointOnCircleEC',
                    elType: 'Circle',
                    paramType: 'aCircle',
                    paramTpl: 'tpl-OnCircle',
                    numberTpl: 'tpl-AngleRadian',
                    numberParamName: 'value',
                });
            }
            return doAwarenessConstruction(docCtrl, construction);
        }

        if (hitEl.type === 'RenderEllipse') {
            const arcRel = hitEl as RenderEllipse;
            const pSRel = docCtrl.rendererCtrl.elementAt(arcRel.f1Idx) as RenderVertex;
            const pERel = docCtrl.rendererCtrl.elementAt(arcRel.f2Idx) as RenderVertex;
            const pS = point(pSRel.coords[0], pSRel.coords[1]);
            const pE = point(pERel.coords[0], pERel.coords[1]);
            const pC = point((pS.x + pE.x) / 2, (pS.y + pE.y) / 2);
            const pM = point(vM[0], vM[1]);
            const vecC0 = vector(pC, point(pC.x + 10, pC.y));
            const vecCE = vector(pC, pE);

            const angle = vecC0.angleTo(vecCE);

            const vecCM = vector(pC, pM);
            const i = intersectionLineEllipse(line(pC, pM), pC, arcRel.a, arcRel.b, angle);

            let p: Point;
            if (i.length === 0) return;
            else if (i.length === 1) {
                p = point(i[0].x, i[0].y);
            } else {
                // choose the farthest intersection along the direction of the cursor drag
                const d = sortOnLineV2(vecCM, i);
                p = point(d[1].x, d[1].y);
            }

            if (preview && i.length > 0) {
                syncPreviewCommands(buildPreviewVertex([p.x, p.y], -120), docCtrl);
                return;
            }

            const ang = vecCE.angleTo(vecCM);
            const construction = await this.buildConstructionHelper({
                docCtrl,
                name: hitEl.name,
                numberParam: ang,
                constructionType: 'Point/PointOnEllipseEC',
                elType: 'Ellipse',
                paramType: 'anEllipse',
                paramTpl: 'tpl-OnEllipse',
                numberTpl: 'tpl-AngleRadian',
                numberParamName: 'value',
            });
            return doAwarenessConstruction(docCtrl, construction);
        }
    }

    private async requestNameAuto(docCtrl: GeoDocCtrl): Promise<string | undefined> {
        const vertex: PreviewVertex = {
            type: 'RenderVertex',
            elType: 'Point',
            relIndex: -120,
            name: '',
            coords: [0, 0],
            renderProp: {} as PointGeoRenderProp,
            usable: true,
            valid: true,
            unselectable: true,
        };
        return (
            await this.requestElementNames(docCtrl, [
                {
                    objName: 'Điểm',
                    originElement: [vertex],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0]?.[0];
    }

    // Helper to generalize construction request creation
    private async buildConstructionHelper(arg: ConstructionArg): Promise<GeoElConstructionRequest | undefined> {
        const {
            docCtrl,
            name,
            numberParam,
            constructionType,
            elType,
            paramType,
            paramTpl,
            numberTpl,
            numberParamName = 'value',
        } = arg;
        const construction = new GeoElConstructionRequest(
            constructionType,
            'Point',
            this.getCGAction(constructionType, elType)
        );

        const pointName = await this.requestNameAuto(docCtrl);
        if (!pointName) return undefined;
        construction.name = pointName;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: paramType,
                optional: false,
                tplStrLangId: paramTpl,
                params: {
                    name: {
                        type: 'singleValue',
                        value: name,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: numberTpl,
                params: {
                    [numberParamName]: {
                        type: 'singleValue',
                        value: numberParam,
                    },
                },
            },
        ];
        return construction;
    }

    // Helper to map constructionType & elType to CG action string
    private getCGAction(constructionType: string, elType: string): string {
        if (constructionType === 'Point/PointOnEllipseEC') return 'OnEllipseWithRadian';
        if (constructionType === 'Point/PointOnCircleEC') return 'OnCircleWithRadian';
        if (constructionType === 'Point/PointOnCircularSectorEC') return 'OnCircularSectorWithRadian';
        if (constructionType === 'Point/PointOnLineEC') {
            if (elType === 'LineSegment') return 'OnLineSegmentWithRatio';
            if (elType === 'Line') return 'OnLineWithCoefficient';
        }
        return '';
    }
}
