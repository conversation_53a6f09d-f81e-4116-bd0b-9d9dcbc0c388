import { DocumentId } from '@viclass/editor.core';
import { DocRenderProp } from './gateway.models';
import { ElRenderPropsProto } from '@viclass/proto/editor.geo';
import { MovementPath } from './render.movement.path';
import { GeoObjectType } from './geo.models';

export type GeoStrokeStyle = 'Solid' | 'Dashed' | 'Dotted' | 'DashedDotted';
export type GeoRelType =
    | 'RenderVertex'
    | 'RenderPolygon'
    | 'RenderLine'
    | 'RenderLineSegment'
    | 'RenderVector'
    | 'RenderRay'
    | 'RenderCircleShape'
    | 'RenderEllipseShape'
    | 'RenderSectorShape'
    | 'RenderAngle'
    | 'RenderSector'
    | 'RenderEllipse'
    | 'RenderCircle';
export type LineType = 'Line' | 'Ray' | 'Segment' | 'Vector';
export type GeoRenderPropType =
    | 'Default'
    | 'Line'
    | 'LineSegment'
    | 'Point'
    | 'Sector'
    | 'Ellipse'
    | 'Circle'
    | 'Polygon'
    | 'PolygonEdge'
    | 'SectorShape'
    | 'CircleShape'
    | 'EllipseShape'
    | 'Angle';

export enum SettingPropertyType {
    color = 'color',
    lineColor = 'lineColor',
    pointColor = 'pointColor',
    strokeStyle = 'strokeStyle',
    lineWeight = 'lineWeight',
    hidden = 'hidden',
    opacity = 'opacity',
    showLabel = 'showLabel',
    labelType = 'labelType',
    label = 'label',
    swapLabelPosition = 'swapLabelPosition',
    spaceFromArcToCorner = 'spaceFromArcToCorner',
    showAngleTypes = 'showAngleTypes',
    angleArc = 'angleArc',
    enableEqualSegmentSign = 'enableEqualSegmentSign',
    equalSegmentSign = 'equalSegmentSign',
    showArcLabel = 'showArcLabel',
    arcLabelType = 'arcLabelType',
    arcLabelContent = 'arcLabelContent',
    isShowAngleSize = 'isShowAngleSize',
    pointLabelType = 'pointLabelType',
    pointLabelFreeContent = 'pointLabelFreeContent',
    showPointLabel = 'showPointLabel',
}

export enum SettingPropertyGroup {
    Default = 'Default',
    Line = 'Line',
    Shape = 'Shape',
    Angle = 'Angle',
    Point = 'Point',
}

export const settingPropertyConfig: {
    [key in SettingPropertyType]: {
        groups: Set<SettingPropertyGroup>;
        multipleGroup: boolean;
        multipleItem: boolean;
    };
} = {
    color: {
        groups: new Set([SettingPropertyGroup.Shape, SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    pointColor: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: true,
        multipleItem: true,
    },
    opacity: {
        groups: new Set([SettingPropertyGroup.Shape]),
        multipleGroup: true,
        multipleItem: true,
    },
    lineColor: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    strokeStyle: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    lineWeight: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    labelType: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: false,
        multipleItem: false,
    },
    showLabel: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    label: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: false,
        multipleItem: false,
    },
    swapLabelPosition: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    enableEqualSegmentSign: {
        groups: new Set([SettingPropertyGroup.Line, SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    equalSegmentSign: {
        groups: new Set([SettingPropertyGroup.Line, SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    spaceFromArcToCorner: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    showAngleTypes: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    angleArc: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    showArcLabel: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    arcLabelType: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    arcLabelContent: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    isShowAngleSize: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    hidden: {
        groups: new Set([
            SettingPropertyGroup.Shape,
            SettingPropertyGroup.Line,
            SettingPropertyGroup.Point,
            SettingPropertyGroup.Angle,
        ]),
        multipleGroup: true,
        multipleItem: true,
    },
    pointLabelType: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: false,
        multipleItem: false,
    },
    pointLabelFreeContent: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: false,
        multipleItem: false,
    },
    showPointLabel: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: true,
        multipleItem: true,
    },
};

export function convertSettingPropertiesToProto(props: {
    [key in SettingPropertyType]?: any;
}): ElRenderPropsProto {
    const proto = new ElRenderPropsProto();

    for (const prop in props) {
        switch (prop) {
            case 'color':
                proto.setColor(props.color);
                break;
            case 'lineColor':
                proto.setLineColor(props.lineColor);
                break;
            case 'pointColor':
                proto.setPointColor(props.pointColor);
                break;
            case 'strokeStyle':
                proto.setStrokeStyle(props.strokeStyle);
                break;
            case 'lineWeight':
                proto.setLineWeight(props.lineWeight);
                break;
            case 'hidden':
                proto.setHidden(props.hidden);
                break;
            case 'opacity':
                proto.setOpacity(props.opacity);
                break;
            case 'showLabel':
                proto.setShowLabel(props.showLabel);
                break;
            case 'labelType':
                proto.setLabelType(props.labelType);
                break;
            case 'label':
                proto.setLabel(props.label);
                break;
            case 'swapLabelPosition':
                proto.setSwapLabelPosition(props.swapLabelPosition);
                break;
            case 'spaceFromArcToCorner':
                proto.setSpaceFromArcToCorner(props.spaceFromArcToCorner);
                break;
            case 'showAngleTypes':
                proto.setShowAngleTypes(props.showAngleTypes);
                break;
            case 'angleArc':
                proto.setAngleArc(props.angleArc);
                break;
            case 'enableEqualSegmentSign':
                proto.setEnableEqualSegmentSign(props.enableEqualSegmentSign);
                break;
            case 'equalSegmentSign':
                proto.setEqualSegmentSign(props.equalSegmentSign);
                break;
            case 'showArcLabel':
                proto.setShowArcLabel(props.showArcLabel);
                break;
            case 'arcLabelType':
                proto.setArcLabelType(props.arcLabelType);
                break;
            case 'arcLabelContent':
                proto.setArcLabelContent(props.arcLabelContent);
                break;
            case 'isShowAngleSize':
                proto.setIsShowAngleSize(props.isShowAngleSize);
                break;
            case 'pointLabelType':
                proto.setPointLabelType(props.pointLabelType);
                break;
            case 'pointLabelFreeContent':
                proto.setPointLabelFreeContent(props.pointLabelFreeContent);
                break;
            case 'showPointLabel':
                proto.setShowPointLabel(props.showPointLabel);
                break;
        }
    }

    return proto;
}

export function convertProtoToSettingProperties(proto: ElRenderPropsProto): {
    [key in SettingPropertyType]?: any;
} {
    const props: { [key in SettingPropertyType]?: any } = {};

    if (proto.hasColor()) props.color = proto.getColor();
    if (proto.hasLineColor()) props.lineColor = proto.getLineColor();
    if (proto.hasPointColor()) props.pointColor = proto.getPointColor();
    if (proto.hasStrokeStyle()) props.strokeStyle = proto.getStrokeStyle();
    if (proto.hasLineWeight()) props.lineWeight = proto.getLineWeight();
    if (proto.hasHidden()) props.hidden = proto.getHidden();
    if (proto.hasOpacity()) props.opacity = proto.getOpacity();
    if (proto.hasShowLabel()) props.showLabel = proto.getShowLabel();
    if (proto.hasLabelType()) props.labelType = proto.getLabelType();
    if (proto.hasLabel()) props.label = proto.getLabel();
    if (proto.hasSwapLabelPosition()) props.swapLabelPosition = proto.getSwapLabelPosition();
    if (proto.hasSpaceFromArcToCorner()) props.spaceFromArcToCorner = proto.getSpaceFromArcToCorner();
    if (proto.hasShowAngleTypes()) props.showAngleTypes = proto.getShowAngleTypes();
    if (proto.hasAngleArc()) props.angleArc = proto.getAngleArc();
    if (proto.hasEnableEqualSegmentSign()) props.enableEqualSegmentSign = proto.getEnableEqualSegmentSign();
    if (proto.hasEqualSegmentSign()) props.equalSegmentSign = proto.getEqualSegmentSign();
    if (proto.hasShowArcLabel()) props.showArcLabel = proto.getShowArcLabel();
    if (proto.hasArcLabelType()) props.arcLabelType = proto.getArcLabelType();
    if (proto.hasArcLabelContent()) props.arcLabelContent = proto.getArcLabelContent();
    if (proto.hasIsShowAngleSize()) props.isShowAngleSize = proto.getIsShowAngleSize();
    if (proto.hasPointLabelType()) props.pointLabelType = proto.getPointLabelType();
    if (proto.hasPointLabelFreeContent()) props.pointLabelFreeContent = proto.getPointLabelFreeContent();
    if (proto.hasShowPointLabel()) props.showPointLabel = proto.getShowPointLabel();

    return props;
}

export enum DocRenderPropType {
    screenUnit = 'screenUnit',
    canvasWidth = 'canvasWidth',
    canvasHeight = 'canvasHeight',
    scale = 'scale',
    translation = 'translation',
    rotation = 'rotation',
    valid = 'valid',

    // background
    background = 'background',
    backgroundColor = 'backgroundColor',

    // shadow
    shadow = 'shadow',
    shadowStyle = 'shadowStyle',

    // grid
    axis = 'axis',
    grid = 'grid',
    detailGrid = 'detailGrid',

    // border
    border = 'border',
    borderStyle = 'borderStyle',
    borderColor = 'borderColor',

    // snap
    snapMode = 'snapMode',
    snapToExistingPoints = 'snapToExistingPoints',
    snapToGrid = 'snapToGrid',

    namingMode = 'namingMode',
}

export type SettingPropertyLabelType = 'size' | 'free';
export type SettingPropertyPointLabelType = 'name' | 'free';
export type SettingPropertyEqualSegmentSign = 'one' | 'two' | 'three';

export class GeoRenderDocState implements DocRenderProp {
    /**
     * Id of the geo doc that generates this render doc
     * When a render doc is created and the geodoc itself has not been persisted
     * then this id might be null.
     */
    docId: DocumentId;

    numDim: number = 2;

    screenUnit: number = 10;
    canvasWidth: number = 200.0;
    canvasHeight: number = 200.0;
    scale: number = 1.0;
    translation: number[] = [0.0, 0.0, 0.0];
    rotation: number[] = [0.0, 0.0, 0.0];
    valid: boolean;

    shadow: boolean;
    shadowStyle: string;

    // grid
    axis: boolean;
    grid: boolean;
    detailGrid: boolean;

    // border
    border: boolean;
    borderStyle: string;
    borderColor: string;

    // snap
    snapMode: boolean;
    snapToExistingPoints: boolean;
    snapToGrid: boolean;

    // naming mode
    namingMode: boolean;
}

export interface IGeoRenderProp {
    type: GeoRenderPropType;
    settingProperties: Set<SettingPropertyType>;
}

export class DefaultGeoRenderProp {
    color?: string;
    lineColor?: string;
    pointColor?: string;
    opacity?: number;
    lineWeight?: number;
    strokeStyle?: GeoStrokeStyle;
    spaceFromArcToCorner?: number;
}

export class GeoRenderProp implements IGeoRenderProp {
    type: GeoRenderPropType = 'Default';
    group: SettingPropertyGroup = SettingPropertyGroup.Default;

    settingProperties = new Set<SettingPropertyType>([]);

    /**
     * Whether an element should be shown. By default,
     * an element that is inferred will be hidden, and
     * an element that is created by user will be shown.
     */
    hidden: boolean = false;

    setHidden<T extends GeoRenderProp>(hidden: boolean): T {
        this.hidden = hidden;
        return this as unknown as T;
    }

    /**
     * Constructs an instance of ElRenderPropsProto from the actual class.
     * This method should be overridden and assigned to `proto` when the render
     * property requires specific field definitions.
     */
    toRenderPropProto(): ElRenderPropsProto {
        const pp = new ElRenderPropsProto();
        pp.setHidden(this.hidden);
        return pp;
    }
}

export class PointGeoRenderProp extends GeoRenderProp implements IGeoRenderProp {
    override type: GeoRenderPropType = 'Point';
    override group: SettingPropertyGroup = SettingPropertyGroup.Point;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.pointColor,
        SettingPropertyType.showPointLabel,
        SettingPropertyType.pointLabelType,
        SettingPropertyType.pointLabelFreeContent,
        SettingPropertyType.hidden,
    ]);

    pointColor: string = '#00aeef';
    showPointLabel: boolean = true;
    pointLabelType: SettingPropertyPointLabelType = 'name';
    pointLabelFreeContent: string = '';

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setPointColor(this.pointColor);
        pp.setShowPointLabel(this.showPointLabel);
        pp.setPointLabelType(this.pointLabelType);
        pp.setPointLabelFreeContent(this.pointLabelFreeContent);
        return pp;
    }
}

export class LineGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'Line';
    override group: SettingPropertyGroup = SettingPropertyGroup.Line;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.lineColor,
        SettingPropertyType.lineWeight,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.showLabel,
        SettingPropertyType.label,
        SettingPropertyType.swapLabelPosition,
        SettingPropertyType.hidden,
    ]);

    lineColor: string = '#00aeef';
    lineWeight: number = 1;
    strokeStyle: GeoStrokeStyle = 'Solid';
    showLabel: boolean = false;
    label: string = '';
    swapLabelPosition: boolean = false;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setLineColor(this.lineColor);
        pp.setLineWeight(this.lineWeight);
        pp.setStrokeStyle(this.strokeStyle);
        pp.setShowLabel(this.showLabel);
        pp.setLabel(this.label);
        pp.setSwapLabelPosition(this.swapLabelPosition);
        return pp;
    }
}

export class LineSegmentGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'LineSegment';
    override group: SettingPropertyGroup = SettingPropertyGroup.Line;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.lineColor,
        SettingPropertyType.lineWeight,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.showLabel,
        SettingPropertyType.labelType,
        SettingPropertyType.label,
        SettingPropertyType.swapLabelPosition,
        SettingPropertyType.enableEqualSegmentSign,
        SettingPropertyType.equalSegmentSign,
        SettingPropertyType.hidden,
    ]);

    lineColor: string = '#00aeef';
    lineWeight: number = 1;
    strokeStyle: GeoStrokeStyle = 'Solid';
    showLabel: boolean = false;
    label: string = '';
    labelType: SettingPropertyLabelType = 'size';
    equalSegmentSign: SettingPropertyEqualSegmentSign = 'one';
    enableEqualSegmentSign: boolean = false;
    swapLabelPosition: boolean = false;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setLineColor(this.lineColor);
        pp.setLineWeight(this.lineWeight);
        pp.setStrokeStyle(this.strokeStyle);
        pp.setShowLabel(this.showLabel);
        pp.setLabelType(this.labelType);
        pp.setLabel(this.label);
        pp.setSwapLabelPosition(this.swapLabelPosition);
        pp.setEnableEqualSegmentSign(this.enableEqualSegmentSign);
        pp.setEqualSegmentSign(this.equalSegmentSign);
        return pp;
    }
}

export class SectorGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'Sector';
    override group: SettingPropertyGroup = SettingPropertyGroup.Line;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.lineColor,
        SettingPropertyType.lineWeight,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.showArcLabel,
        SettingPropertyType.arcLabelType,
        SettingPropertyType.arcLabelContent,
        SettingPropertyType.hidden,
    ]);

    lineColor: string = '#00aeef';
    lineWeight: number = 1;
    strokeStyle: GeoStrokeStyle = 'Solid';
    showArcLabel: boolean = false;
    arcLabelType: string = '';
    arcLabelContent: string = '';

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setLineColor(this.lineColor);
        pp.setLineWeight(this.lineWeight);
        pp.setStrokeStyle(this.strokeStyle);
        pp.setShowArcLabel(this.showArcLabel);
        pp.setArcLabelType(this.arcLabelType);
        pp.setArcLabelContent(this.arcLabelContent);
        return pp;
    }
}

export class EllipseGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'Ellipse';
    override group: SettingPropertyGroup = SettingPropertyGroup.Line;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.lineColor,
        SettingPropertyType.lineWeight,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.label,
        SettingPropertyType.hidden,
    ]);

    lineColor: string = '#00aeef';
    lineWeight: number = 1;
    strokeStyle: GeoStrokeStyle = 'Solid';
    label: string = '';

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setLineColor(this.lineColor);
        pp.setLineWeight(this.lineWeight);
        pp.setStrokeStyle(this.strokeStyle);
        pp.setLabel(this.label);
        return pp;
    }
}

export class CircleGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'Circle';
    override group: SettingPropertyGroup = SettingPropertyGroup.Line;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.lineColor,
        SettingPropertyType.lineWeight,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.label,
        SettingPropertyType.hidden,
    ]);

    lineColor: string = '#00aeef';
    lineWeight: number = 1;
    strokeStyle: GeoStrokeStyle = 'Solid';
    label: string = '';

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setLineColor(this.lineColor);
        pp.setLineWeight(this.lineWeight);
        pp.setStrokeStyle(this.strokeStyle);
        pp.setLabel(this.label);
        return pp;
    }
}

export class PolygonGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'Polygon';
    override group: SettingPropertyGroup = SettingPropertyGroup.Shape;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.color,
        SettingPropertyType.opacity,
        SettingPropertyType.hidden,
    ]);

    color: string = '#00aeef';
    opacity: number = 5;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setColor(this.color);
        pp.setOpacity(this.opacity);
        return pp;
    }
}

export class CircleShapeGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'CircleShape';
    override group: SettingPropertyGroup = SettingPropertyGroup.Shape;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.color,
        SettingPropertyType.opacity,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.lineWeight,
        SettingPropertyType.hidden,
    ]);

    color: string = '#00aeef';
    opacity: number = 5;
    strokeStyle: GeoStrokeStyle = 'Solid';
    lineWeight: number = 1;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setStrokeStyle(this.strokeStyle);
        pp.setLineWeight(this.lineWeight);
        pp.setColor(this.color);
        pp.setOpacity(this.opacity);
        return pp;
    }
}

export class EllipseShapeGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'EllipseShape';
    override group: SettingPropertyGroup = SettingPropertyGroup.Shape;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.color,
        SettingPropertyType.opacity,
        SettingPropertyType.strokeStyle,
        SettingPropertyType.lineWeight,
        SettingPropertyType.hidden,
    ]);

    color: string = '#00aeef';
    opacity: number = 5;
    strokeStyle: GeoStrokeStyle = 'Solid';
    lineWeight: number = 1;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setStrokeStyle(this.strokeStyle);
        pp.setLineWeight(this.lineWeight);
        pp.setColor(this.color);
        pp.setOpacity(this.opacity);
        return pp;
    }
}

export class PolygonEdgeGeoRenderProp extends GeoRenderProp {
    override type: GeoRenderPropType = 'PolygonEdge';
    override group: SettingPropertyGroup = SettingPropertyGroup.Shape;
    override settingProperties = new Set<SettingPropertyType>([SettingPropertyType.color, SettingPropertyType.opacity]);

    color: string = '#00aeef';
    opacity: number = 5;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setColor(this.color);
        pp.setOpacity(this.opacity);
        return pp;
    }
}

export class AngleGeoRenderProp extends GeoRenderProp implements IGeoRenderProp {
    override type: GeoRenderPropType = 'Angle';
    override group: SettingPropertyGroup = SettingPropertyGroup.Angle;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.color,
        SettingPropertyType.opacity,
        SettingPropertyType.spaceFromArcToCorner,
        SettingPropertyType.angleArc,
        SettingPropertyType.showAngleTypes,
        SettingPropertyType.enableEqualSegmentSign,
        SettingPropertyType.equalSegmentSign,
        SettingPropertyType.isShowAngleSize,
        SettingPropertyType.hidden,
    ]);

    color: string = '#00aeef';
    opacity: number = 5;
    isShowAngleSize: boolean = false;
    showAngleTypes: string = 'as2';
    angleArc: number = 1;
    enableEqualSegmentSign: boolean = false;
    equalSegmentSign: SettingPropertyEqualSegmentSign = 'one';
    spaceFromArcToCorner: number = 25;

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setIsShowAngleSize(this.isShowAngleSize);
        pp.setShowAngleTypes(this.showAngleTypes);
        pp.setAngleArc(this.angleArc);
        pp.setSpaceFromArcToCorner(this.spaceFromArcToCorner);
        pp.setEnableEqualSegmentSign(this.enableEqualSegmentSign);
        pp.setEqualSegmentSign(this.equalSegmentSign);
        pp.setColor(this.color);
        pp.setOpacity(this.opacity);
        return pp;
    }
}

export class SectorShapeGeoRenderProp extends GeoRenderProp implements IGeoRenderProp {
    override type: GeoRenderPropType = 'SectorShape';
    override group: SettingPropertyGroup = SettingPropertyGroup.Shape;

    override settingProperties = new Set<SettingPropertyType>([
        SettingPropertyType.color,
        SettingPropertyType.opacity,
        SettingPropertyType.showArcLabel,
        SettingPropertyType.arcLabelType,
        SettingPropertyType.arcLabelContent,
        SettingPropertyType.hidden,
    ]);

    color: string = '#00aeef';
    opacity: number = 5;
    showArcLabel: boolean = false;
    arcLabelType: string = '';
    arcLabelContent: string = '';

    override toRenderPropProto(): ElRenderPropsProto {
        const pp = super.toRenderPropProto();
        pp.setShowArcLabel(this.showArcLabel);
        pp.setArcLabelType(this.arcLabelType);
        pp.setArcLabelContent(this.arcLabelContent);
        pp.setColor(this.color);
        pp.setOpacity(this.opacity);
        return pp;
    }
}

export abstract class GeoRenderElement {
    // type of render element
    abstract readonly type: GeoRelType;

    // type of element that this render element represent for
    abstract readonly elType: GeoObjectType;

    abstract readonly renderProp: GeoRenderProp;

    unselectable?: boolean;
    relIndex: number;
    name: string = '';
    area?: number = 0;
    perimeter?: number = 0;
    length?: number = 0;
    vertexRelIdxes?: number[] = [];
    lineRelIdxes?: number[] = [];
    arcRelIdx?: number;

    /**
     * Whether an element is usable or not.
     */
    usable: boolean = true;

    /**
     * Whether an element is usable or not.
     */
    deleted?: boolean;

    /**
     * Whether an element is valid or not
     */
    valid: boolean = true;
}

export abstract class GeoPreviewElement {
    abstract readonly type: GeoRelType;
    abstract readonly elType: GeoObjectType;
    abstract readonly renderProp: GeoRenderProp;

    unselectable?: boolean = true;
    relIndex: number;
    name: string = '';
    area?: number = 0;
    perimeter?: number = 0;
    length?: number = 0;

    usable: boolean = true; // Whether an element is usable or not
    valid: boolean = true; // Whether an element is valid or not
}

export class RenderVertex extends GeoRenderElement {
    constructor(public override readonly renderProp: PointGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderVertex';
    override readonly elType: GeoObjectType = 'Point';
    coords: number[] = [];
    movementPath?: MovementPath = undefined; // null meaning unable to move
}

export class PreviewVertex extends GeoPreviewElement {
    constructor(public override readonly renderProp: PointGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderVertex';
    override readonly elType: GeoObjectType = 'Point';
    coords: number[] = [];
}

export class RenderLine extends GeoRenderElement {
    constructor(public override renderProp: LineGeoRenderProp) {
        super();
    }

    override type: GeoRelType = 'RenderLine';
    override elType: GeoObjectType = 'LineVi';
    startPointIdx: number = -1;
    endPointIdx: number = -1;
    vector: number[] = [];
}

export class PreviewLine extends GeoPreviewElement {
    constructor(public override readonly renderProp: LineGeoRenderProp) {
        super();
    }

    override type: GeoRelType = 'RenderLine';
    override elType: GeoObjectType = 'LineVi';
    startPoint: number[];
    endPoint: number[];
    vector?: number[] = [];
}

export class RenderLineSegment extends RenderLine {
    constructor(public override readonly renderProp: LineSegmentGeoRenderProp) {
        super(renderProp);
    }

    override readonly type: GeoRelType = 'RenderLineSegment';
    override readonly elType: GeoObjectType = 'LineSegment';
}

export class PreviewLineSegment extends PreviewLine {
    constructor(public override readonly renderProp: LineSegmentGeoRenderProp) {
        super(renderProp);
    }

    override readonly type: GeoRelType = 'RenderLineSegment';
    override readonly elType: GeoObjectType = 'LineSegment';
}

export class RenderRay extends RenderLine {
    override readonly type: GeoRelType = 'RenderRay';
    override readonly elType: GeoObjectType = 'Ray';
}

export class PreviewRay extends PreviewLine {
    override readonly type: GeoRelType = 'RenderRay';
    override readonly elType: GeoObjectType = 'Ray';
}

export class RenderVector extends RenderLine {
    override readonly type: GeoRelType = 'RenderVector';
    override readonly elType: GeoObjectType = 'VectorVi';

    // render fields
    point1?: number[];
    point2?: number[];
}

export class PreviewVector extends PreviewLine {
    override readonly type: GeoRelType = 'RenderVector';
    override readonly elType: GeoObjectType = 'VectorVi';
}

export class RenderCircleShape extends GeoRenderElement {
    constructor(public override readonly renderProp: CircleShapeGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderCircleShape';
    override readonly elType: GeoObjectType = 'Circle';
    radius: number = 0.0;
    centerPointIdx: number = 0; // index of the vertex that is the center
}

export class PreviewCircleShape extends GeoPreviewElement {
    constructor(public override readonly renderProp: CircleShapeGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderCircleShape';
    override readonly elType: GeoObjectType = 'Circle';
    radius: number = 0.0;
    centerPoint: number[];
}

export class RenderEllipseShape extends GeoRenderElement {
    constructor(public override readonly renderProp: EllipseShapeGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderEllipseShape';
    override readonly elType: GeoObjectType = 'Ellipse';
    f1Idx: number = 0;
    f2Idx: number = 0;
    a: number = 0.0;
    b: number = 0.0;
    rotate: number = 0.0;
}

export class PreviewEllipseShape extends GeoPreviewElement {
    constructor(public override readonly renderProp: EllipseShapeGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderEllipseShape';
    override readonly elType: GeoObjectType = 'Ellipse';
    a: number = 0.0;
    b: number = 0.0;
    rotate: number = 0.0;
    f1: number[];
    f2: number[];
}

export class RenderSector extends GeoRenderElement {
    constructor(public override readonly renderProp: SectorGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderSector';
    override elType: GeoObjectType = 'CircularSector';
    centerPointIdx: number = -1;
    startPointIdx?: number = -1;
    endPointIdx?: number = -1;
    radius: number = 0;
}

export class PreviewSector extends GeoPreviewElement {
    constructor(public override readonly renderProp: SectorGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderSector';
    override elType: GeoObjectType = 'CircularSector';
    centerPoint: number[];
    startPoint: number[];
    endPoint: number[];
}

export class RenderEllipse extends GeoRenderElement {
    constructor(public override readonly renderProp: EllipseGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderEllipse';
    override readonly elType: GeoObjectType = 'Ellipse';
    f1Idx: number = 0;
    f2Idx: number = 0;
    a: number = 0.0;
    b: number = 0.0;
    rotate: number = 0.0;

    // render fields
    labelAngle?: number;
}

export class RenderCircle extends GeoRenderElement {
    constructor(public override readonly renderProp: CircleGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderCircle';
    override elType: GeoObjectType = 'Circle';

    centerPointIdx: number = -1;
    endPointIdx?: number = -1;
    radius: number = 0;

    // render fields
    labelAngle?: number;
}

export class RenderSectorShape extends GeoRenderElement {
    constructor(public override readonly renderProp: SectorShapeGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderSectorShape';
    override elType: GeoObjectType = 'CircularSector';
    centerPointIdx: number = -1;
    startPointIdx: number = -1;
    endPointIdx: number = -1;
    radius: number = 0;
}

export class PreviewSectorShape extends GeoPreviewElement {
    constructor(public override readonly renderProp: SectorShapeGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderSectorShape';
    override elType: GeoObjectType = 'CircularSector';
    centerPoint: number[];
    startPoint: number[];
    endPoint: number[];
}

export class RenderPolygon extends GeoRenderElement {
    constructor(public override readonly renderProp: PolygonGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderPolygon';
    override elType: GeoObjectType = 'Polygon';
    faces: number[] = [];
}

export class PreviewPolygon extends GeoPreviewElement {
    constructor(public override readonly renderProp: PolygonGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderPolygon';
    override elType: GeoObjectType = 'Polygon';
    faces: number[][];
}

export class RenderAngle extends GeoRenderElement {
    constructor(public override readonly renderProp: AngleGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderAngle';
    override readonly elType: GeoObjectType = 'Angle';
    anglePointIdx: number = -1;
    vectorStart: number[] = [0.0, 0.0, 0.0];
    vectorEnd: number[] = [0.0, 0.0, 0.0];
    degree: number = 0.0;

    // render fields
    startAngleInRad?: number;
    endAngleInRad?: number;
    angle?: number;
    centerAngle?: number;
    labelAngle?: number;
}

export class PreviewAngle extends GeoPreviewElement {
    constructor(public override readonly renderProp: AngleGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderAngle';
    override readonly elType: GeoObjectType = 'Angle';
    anglePoint: number[];
    startPoint?: number[];
    vStart: number[];
    endPoint?: number[];
    vEnd: number[];
    degree: number = 0.0;
}
