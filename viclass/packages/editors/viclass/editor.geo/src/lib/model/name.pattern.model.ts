import { GeoRelType } from './render.elements.model';

export declare type NamePatternItem = {
    relType: GeoRelType[];
    name: string;
    regex: RegExp[];
};

export const RenderNamePatternModel = {
    RenderVertex: {
        relType: ['Vertex'],
        name: '<PERSON><PERSON><PERSON><PERSON>',
        regex: [/^[A-Z]\d*'?$/],
    },
    Line: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đường thẳng',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)*$/, /^[a-z]\d*'?$/, /^([A-Z]\d*'?)([a-z]\d*'?)$/],
    },
    LineSegment: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đoạn thẳng',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    Vector: {
        relType: ['Line', 'LineSegment', 'Vector', '<PERSON>'],
        name: 'Vector',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    Ray: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Tia',
        regex: [/^([A-Z]\d*'?)([A-Za-z]\d*'?)$/],
    },
    Polygon: {
        relType: ['Polygon'],
        name: 'Đa giác',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)*$/],
    },
    Triangle: {
        relType: ['Polygon'],
        name: 'Tam giác',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    Quadrilateral: {
        relType: ['Polygon'],
        name: 'Tứ giác',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderCircleShape: {
        relType: ['Circle'],
        name: 'hình tròn',
        regex: [/^[a-z]\d*'?$/],
    },
    Ellipse: {
        relType: ['Ellipse'],
        name: 'Hình elip',
        regex: [/^([A-Z]\d*'?)$/],
    },
    Sector: {
        relType: ['CircularSector'],
        name: 'Hình quạt',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    Semicircle: {
        relType: ['CircularSector'],
        name: 'Hình bán nguyệt',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    Angle: {
        relType: ['Angle'],
        name: 'Góc',
        regex: [/^([A-Z]\d*'?)$/, /^([A-Za-z]\d*'?)([A-Z]\d*'?)([A-Za-z]\d*'?)$/],
    },
};
