/**
 * Contains all function that is used to transform a word document.
 *
 * VERY IMPORTANT NOTE:
 *
 * The implementation of the functions SHOULD only be related DOM transformation
 * and not using anything from doc ctrl or using selection API because the transformation
 * here is used ALSO on the server.
 *
 */

import {
    BeforeInputCmdProto,
    InsertViewportCmdProto,
    ReplaceContentCmdProto,
    UpdateInternalDocMappingCmdProto,
    UpdateViewportViewStateCmdProto,
} from '@viclass/proto/editor.word';
import { IWordDocNode } from './model';

// --- Collapse, text insertion -----

// --- Check if new content needs to be wrapped
function needsParagraph() {}

/**
 * Given a container node, insert text data from a command into the container node, return
 * the text node (either created new, or existing text node within the container) and the end location
 * of the insertion (which serves as the new location of the caret)
 * @param state
 */
export function insertTextDataToContainer(
    htmlDoc: Document,
    contIdxNode: IWordDocNode,
    state: BeforeInputCmdProto
): {
    node: Text;
    endLocation: number;
} {
    const cont = contIdxNode.underlying;
    const offset = state.getStartOffset();
    let data = state.getData();

    data = data.replace(/  /g, ' \u00A0');

    if (cont.nodeName == '#text') {
        const r = insertDataIntoTextNode(cont as Text, data, offset);
        return { node: cont as Text, endLocation: r.endLocation };
    } else {
        // multiple cases to be taken care of, because if start container is not a text
        // the tree indexes of its node needs to be recalculated, for example, if this is
        // a select that span multiple nodes.

        // let's find the wordoc node of the child
        const wdChildNode = contIdxNode.childAt(offset);
        const child = wdChildNode ? wdChildNode.underlying : null;

        if (wdChildNode && child != cont.childNodes.item(offset)) {
            throw new Error(
                'Something wrong. The targeted child is not the same as the node recorded inside the index. There might be some mismatch between the index tree and the actual underlying HTML document.'
            );
        }

        if (child && child.nodeName == '#text') {
            const r = insertDataIntoTextNode(child as Text, data, 0);
            return { node: child as Text, endLocation: r.endLocation };
        } else {
            const text = htmlDoc.createTextNode(data);

            if (child) {
                cont.insertBefore(text, child);
                contIdxNode.insertBefore(contIdxNode.createNode(text), wdChildNode, true);
            } else {
                // console.log('Appending text node to ', cont.tagName);
                cont.appendChild(text);
                contIdxNode.insert(contIdxNode.createNode(text), null, true);
            }

            return { node: text, endLocation: data.length };
        }
    }
}

/**
 * Insert string into a text node at a particular offset and return the end location of the inserted data
 *
 * Note that, the end location is part of the same text node
 * @param t
 * @param data
 * @param offset
 */
export function insertDataIntoTextNode(
    t: Text,
    data: string,
    offset: number
): {
    endLocation: number;
} {
    if (offset > 0 && t.data.charAt(offset - 1) == ' ' && data.charAt(0) == ' ') {
        data = '\u00A0' + data.substring(1);
    }
    if (offset < t.data.length && t.data.charAt(offset) == ' ' && data.charAt(data.length - 1) == ' ') {
        data = data.substring(0, data.length - 1) + '\u00A0';
    }
    if (offset == t.data.length) {
        if (data.endsWith(' ')) data = data.slice(0, data.length - 1) + '\u00A0';
        t.appendData(data);

        if (t.data.endsWith('\u00A0\u00A0') && !(t.data.length > 2 && t.data.charAt(t.data.length - 3) == ' '))
            t.data = t.data.slice(0, t.data.length - 2) + ' \u00A0';

        return { endLocation: t.data.length };
    } else {
        t.insertData(offset, data); // no index change
        return { endLocation: offset + data.length };
    }
}

//---- Collapse, text delete backward ---

export function deleteTextFromContainer(
    htmlDoc: Document,
    contIdxNode: IWordDocNode,
    state: BeforeInputCmdProto
): {
    node: Node;
    endLocation: number;
} {
    const cont = contIdxNode.underlying;
    const startOffset = state.getStartOffset();
    const endOffset = state.getEndOffset();

    if (cont.nodeName == '#text') {
        // have to use this comparison, because inside iframe nodes use different classes from the outer window
        const tNode = cont as Text;
        if (startOffset >= 0) {
            tNode.data = tNode.data.slice(0, startOffset) + tNode.data.slice(endOffset);
            // handle duplicate spaces
            if (startOffset < tNode.data.length && startOffset > 0 && endOffset < tNode.data.length) {
                const c = tNode.data.charAt(startOffset - 1);
                const c1 = tNode.data.charAt(endOffset - 1);

                if (c == ' ' && c1 == ' ') {
                    tNode.data = tNode.data.slice(0, startOffset - 1) + '\u00A0' + tNode.data.slice(endOffset - 1);
                }
            }
        }
        return {
            node: tNode,
            endLocation: endOffset - 1,
        };
    } else {
        return null;
        // TODO: Deleting backward with non-text container has not been implemented yet
    }
}

//--- Collapse insert viewport ---

export function insertViewportToContainer(
    htmlDoc: Document,
    contIdxNode: IWordDocNode,
    state: InsertViewportCmdProto,
    viewportElClass: string
): { node: Node; endLocation: number } {
    const vpDiv = htmlDoc.createElement('div');

    vpDiv.setAttribute('contenteditable', 'false');
    vpDiv.setAttribute('id', state.getViewportId());
    vpDiv.setAttribute('tabindex', '-1');
    vpDiv.setAttribute('data-ed-type', state.getEdtype());

    vpDiv.classList.add(viewportElClass);

    vpDiv.style.width = state.getWidth();
    vpDiv.style.height = state.getHeight();
    vpDiv.style.position = 'relative';
    vpDiv.style.border = '1px solid red'; // to be removed
    vpDiv.style.setProperty('user-select', 'true');

    const underlying = contIdxNode.underlying;
    const offset = state.getStartOffset();

    // insertion of the viewport containing element into the word document
    // need to reindex the tree as well
    if (underlying.nodeName == '#text') {
        const cont = underlying as Text;
        const textNode = cont.splitText(offset);
        const parent = contIdxNode.parent;
        const textNodeIdx = parent.createNode(textNode);
        parent.insert(textNodeIdx, contIdxNode, false);
        const viewportNodeIdx = parent.createNode(vpDiv);
        parent.underlying.insertBefore(vpDiv, textNode);
        // insert the index node of the viewport between the two text node
        parent.insert(viewportNodeIdx, contIdxNode, true);
    } else {
        // multiple cases to be taken care of, because if start container is not a text
        // the tree indexes of its node needs to be recalculated, for example, if this is
        // a select that span multiple nodes.

        // let's find the wordoc node of the child
        const wdChildNode = contIdxNode.childAt(offset);
        const child = wdChildNode ? wdChildNode.underlying : null;

        if (wdChildNode && child != underlying.childNodes.item(offset)) {
            throw new Error(
                'Something wrong. The targeted child is not the same as the node recorded inside the index. There might be some mismatch between the index tree and the actual underlying HTML document.'
            );
        }

        if (child) {
            underlying.insertBefore(vpDiv, child);
            contIdxNode.insertBefore(contIdxNode.createNode(vpDiv), wdChildNode, true);
        } else {
            underlying.appendChild(vpDiv);
            contIdxNode.insert(contIdxNode.createNode(vpDiv));
        }
    }

    return { node: vpDiv, endLocation: 0 };
}

// update doc mapping
export function updateDocMapping(htmlDoc: Document, vpElId: string, cmd: UpdateInternalDocMappingCmdProto) {
    const rootEl = htmlDoc.getElementById(vpElId);
    if (rootEl) {
        rootEl.setAttribute('data-global-id', cmd.getInternalDocGlobalId());
        rootEl.setAttribute('data-local-id', cmd.getInternalDocLocalId() + '');
    } else {
        throw new Error(`Root element for viewport id ${vpElId} cannot be found.`);
    }
}

export function updateViewportViewState(htmlDoc: Document, vpElId: string, cmd: UpdateViewportViewStateCmdProto) {
    const rootEl = htmlDoc.getElementById(vpElId);
    if (rootEl) {
        if (cmd.hasLookAt()) {
            rootEl.setAttribute('data-look-at-x', Math.round(cmd.getLookAt().getX()).toString());
            rootEl.setAttribute('data-look-at-y', Math.round(cmd.getLookAt().getY()).toString());
        }

        if (cmd.getZoom() != 0) {
            rootEl.setAttribute('data-zoom', cmd.getZoom().toString());
        }
    } else {
        throw new Error(`Root element for viewport id ${vpElId} cannot be found.`);
    }
}

// replace content of a node
export function replaceContent(htmlDoc: Document, cmd: ReplaceContentCmdProto, contIdxNode: IWordDocNode) {
    const u = contIdxNode.underlying as HTMLElement;
    u.innerHTML = cmd.getContent();
    contIdxNode.rebuildSubTree();
}
