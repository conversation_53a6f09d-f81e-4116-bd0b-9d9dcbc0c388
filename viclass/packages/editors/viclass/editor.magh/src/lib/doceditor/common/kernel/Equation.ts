import { isNil } from 'lodash';
import { MaghDocRenderProp } from '../../../model';
import { EuclidianView } from '../../chart/EuclidianView';
import { Destroyable, Drawable } from '../../chart/models';
import { Plot } from '../../chart/Plot';
import { EquationAnalyzer } from './analyzers';
import { Engine } from './Engine';
import { RangeScope } from './scope/RangeScope';
import { ScopeVariable } from './scope/ScopeVariable';
import { BoxedExpr, EqType, EquationStyle, GlobalScopeGetter, MathJsonExpr, SerializedEquation } from './types';

/**
 * Wrapper for math expression.
 * Will parse the math expression into MathJSON, analyze the equation, and compile it.
 */
export class Equation implements Drawable, Destroyable {
    static readonly X_VAR = 'x';
    static readonly Y_VAR = 'y';
    static readonly PARAMETRIC_VAR = 't';

    private _raw!: string;
    private _lhs?: BoxedExpr;
    private _rhs?: BoxedExpr;
    private _compiledLhs?: (args: Record<string, any>) => number | undefined;
    private _compiledRhs?: (args: Record<string, any>) => number | undefined;

    public readonly internalScope = new RangeScope(0, 1);
    public readonly globalScope = new ScopeVariable(this);

    private _analyzer: EquationAnalyzer;

    private _plot: Plot;

    public hidden = false;

    public styles: EquationStyle = {
        color: 'red',
        lineWidth: 2,
    };

    get rawExpression(): string {
        return this._raw;
    }

    get isValid(): boolean {
        return this._analyzer.valid;
    }

    get isImplicit(): boolean {
        return this._analyzer.implicit;
    }

    get isParametricArc(): boolean {
        return this._analyzer.parametricCurve;
    }

    get lhs(): BoxedExpr | undefined {
        return this.isValid ? this._lhs : undefined;
    }

    get rhs(): BoxedExpr | undefined {
        return this.isValid ? this._rhs : undefined;
    }

    get equationType(): EqType {
        return this._analyzer.eqType;
    }

    get supportInternalScope(): boolean {
        return this.isValid && this.equationType === EqType.Plot && this.isParametricArc;
    }

    get supportGlobalScope(): boolean {
        return this.isValid && this.equationType === EqType.ScopeVar;
    }

    constructor(
        private engine: Engine,
        private scopeGetter: GlobalScopeGetter
    ) {
        this.setExpression('');
        this._analyzer = new EquationAnalyzer(this, this.engine);
        this._plot = new Plot(this);
        this._analyzer.analyze();
    }

    destroy(): void {
        this.reset();
    }

    updatePath(view: EuclidianView) {
        this._plot.updatePath(view);
    }

    draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        if (this.hidden || !this.isValid) return;
        this._plot.draw(ctx, view, drp);
    }

    public getEvaluateScope() {
        return this.scopeGetter(this);
    }

    public setExpression(rawExpr: string): boolean {
        this._raw = rawExpr;
        if (!rawExpr) return false;

        try {
            // analyze and apply transformation if necessary
            const mathJson = this._analyzer.analyze();
            if (!this.isValid) return false;

            this.compileEquation(mathJson);
            this.adjustGlobalScope();
            return true;
        } catch (e) {
            this.reset();

            console.warn('compile failed', e);
            return false;
        }
    }

    public evaluateLhs(scope: Record<string, any>): number {
        if (!this.isValid || !this._compiledLhs) return NaN;
        const globalScope = this.getEvaluateScope();
        return (
            this._compiledLhs({
                ...globalScope,
                ...scope,
            }) ?? NaN
        );
    }

    public evaluateRhs(scope: Record<string, any>): number {
        if (!this.isValid || !this._compiledRhs) return NaN;
        const globalScope = this.getEvaluateScope();
        return (
            this._compiledRhs({
                ...globalScope,
                ...scope,
            }) ?? NaN
        );
    }

    /**
     * Simplify the equation by move the right-hand side to the left-hand side.
     * So we will only need to evaluate the left-hand side on sampling
     * Ex: 'x>y' -> lhs: 'x-y', rhs: 0
     */
    private compileEquation(mathJson: MathJsonExpr): void {
        if (!this.isValid) return;

        let lhs: MathJsonExpr;
        let rhs: MathJsonExpr;

        if (this._analyzer.onlyLhs) {
            lhs = mathJson;
            rhs = 0;
        } else if (this._analyzer.parametricCurve) {
            // ["Pair", `f1(t)`, `f2(t)`]
            const sequence = mathJson as MathJsonExpr[];
            lhs = sequence[1] as MathJsonExpr;
            rhs = sequence[2] as MathJsonExpr;
        } else {
            if (!Array.isArray(mathJson)) throw new Error('invalid equation for simplify');

            lhs = mathJson[1] as MathJsonExpr;
            rhs = mathJson[2] as MathJsonExpr;

            if (rhs !== 0 && this.equationType !== EqType.ScopeVar) {
                lhs = ['Add', lhs, ['Negate', rhs]];
                rhs = 0;
            }
        }

        this._lhs = this.engine.box(lhs);
        this._rhs = this.engine.box(rhs);

        this._compiledLhs = this.lhs?.compile() as any;
        this._compiledRhs = this.rhs?.compile() as any;
    }

    private adjustGlobalScope(): void {
        if (!this.supportGlobalScope) return;

        const val = this.globalScope.value;
        if (val < this.globalScope.min) {
            this.globalScope.min = Math.floor(val);
        }
        if (val > this.globalScope.max) {
            this.globalScope.max = Math.ceil(val);
        }
        const rangeDiff = this.globalScope.max - this.globalScope.min;
        if (rangeDiff > 0 && rangeDiff < this.globalScope.step) {
            let newStep = rangeDiff / 10;
            newStep = Math.ceil(newStep * 100) / 100;
            this.globalScope.step = newStep;
        }
    }

    private reset(): void {
        this._lhs = undefined;
        this._rhs = undefined;
        this._compiledLhs = undefined;
        this._compiledRhs = undefined;
        this._analyzer.reset();
    }

    isPreferInterval(): boolean {
        return !this.isParametricArc && this._analyzer.preferInterval;
    }

    serialize(): SerializedEquation {
        return {
            expression: this.rawExpression,
            hidden: this.hidden,
            internalScope: {
                min: this.internalScope.min,
                max: this.internalScope.max,
            },
            globalScope: {
                min: this.globalScope.min,
                max: this.globalScope.max,
                step: this.globalScope.step,
            },
            styles: this.styles,
        };
    }

    deserialize(serialized: SerializedEquation): void {
        this.setExpression(serialized.expression);

        if (!isNil(serialized.hidden)) {
            this.hidden = serialized.hidden;
        }
        if (!isNil(serialized.internalScope)) {
            this.internalScope.min = serialized.internalScope.min;
            this.internalScope.max = serialized.internalScope.max;
        }
        if (!isNil(serialized.globalScope)) {
            this.globalScope.min = serialized.globalScope.min;
            this.globalScope.max = serialized.globalScope.max;
            this.globalScope.step = serialized.globalScope.step;
        }
        if (!isNil(serialized.styles)) {
            this.styles = serialized.styles;
        }
    }
}
