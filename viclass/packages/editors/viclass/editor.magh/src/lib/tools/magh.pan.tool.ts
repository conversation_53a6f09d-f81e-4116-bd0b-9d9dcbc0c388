import {
    DefaultMouseEventData,
    DefaultPointerEventData,
    MouseEventData,
    mouseLocation,
    NativeEventTarget,
    PanFeature,
    PointerEventData,
    SupportPanFeature,
} from '@viclass/editor.core';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphEditor, MathGraphToolType } from '../magh.api';
import { MathGraphTool } from './magh.tool';
import { MaghPanToolState } from './models';
import { validatePointerPos } from './tool.utils';

export class MathGraphPanTool extends MathGraphTool<MaghPanToolState> implements SupportPanFeature {
    override toolState: MaghPanToolState = { doc: undefined };
    constructor(
        editor: MathGraphEditor,
        private panFeature: PanFeature
    ) {
        super(editor);
    }

    get toolType(): MathGraphToolType {
        return 'MaghPanTool';
    }

    override resetState() {
        this.clear();
    }

    clear() {
        this.toolState.doc = undefined;
        this.started = false;
        this.lastPointerMove = undefined;
    }

    override onBlur() {
        this.clear();
    }

    override handleMouseEvent(event: MouseEventData<NativeEventTarget<any>>): MouseEventData<NativeEventTarget<any>> {
        return event;
    }

    override onAttachViewport() {
        this.panFeature.registerPanHandler(this.toolbar.viewport.id, this.editor.editorType, this);
    }

    isPanHandleAble(docCtrl: MathGraphDocCtrl, event: PointerEventData<NativeEventTarget<any>>): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        try {
            if (event instanceof DefaultPointerEventData) {
                const mousePos = mouseLocation(event);
                if (!validatePointerPos(mousePos, docCtrl)) {
                    return false;
                }
                return true;
            }
        } catch (error) {
            return false;
        }

        return false;
    }

    isPanHandling(docCtrl: MathGraphDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        if (this.toolState.doc === docCtrl) return true;
        return false;
    }

    startPan(docCtrl: MathGraphDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        this.toolState.doc = docCtrl;
        this.started = true;
    }

    stopPan(docCtrl: MathGraphDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        if (this.toolState.doc !== docCtrl) return;

        this.clear();
    }

    translateViewpoint(docCtrl: MathGraphDocCtrl, deltaXInScreen: number, deltaYInScreen: number) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        if (docCtrl !== this.toolState.doc) return;
        const docRenderProp = this.toolState.doc.state.docRenderProp;
        const chartView = docCtrl.docEditor.getChartView();
        const x =
            docRenderProp.translation[0] + chartView.layerToChartLength(deltaXInScreen * chartView.viewport.zoomLevel);
        const y =
            docRenderProp.translation[1] + chartView.layerToChartLength(deltaYInScreen * chartView.viewport.zoomLevel);

        docCtrl.lookAt(x, y);

        // update toolstate
        this.toolbar.update('MaghPanTool', this.toolState);
    }
}
