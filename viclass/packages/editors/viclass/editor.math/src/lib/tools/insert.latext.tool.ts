import { ToolState } from '@viclass/editor.core';
import { LatexState } from '../doceditor/math.doc.editor';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { MathEditor } from '../math.editor';
import { MathToolDocListener } from '../math.models';
import { MathSyntaxError } from '../model/math.doc';
import { MathTool } from './math.tool';
import { MathToolType } from './models';

export interface MathContext extends ToolState {
    latex: string;
    errors: MathSyntaxError[];
}

const DEFAULT_CONTEXT: MathContext = {
    latex: '',
    errors: [],
};

export class InsertLatexTool extends MathTool<ToolState> implements MathToolDocListener {
    override toolType: MathToolType = 'InsertLatexTool';
    override toolState: MathContext = { ...DEFAULT_CONTEXT };

    private focusedDocId: string = '';
    private unsubscribe: Function = undefined;

    constructor(editor: MathEditor) {
        super(editor);
    }

    insertLatex(latex: string) {
        this.executeInFocusedDocCtrl((docCtrl: MathDocCtrl) => {
            docCtrl.docEditor.insertLatex(latex);
            docCtrl.docEditor.focus();
        });
    }

    setContent(latex: string) {
        this.executeInFocusedDocCtrl((docCtrl: MathDocCtrl) => {
            docCtrl.setContent(latex, true);
            docCtrl.saveContentUpdate();
        });
    }

    override onEnable(): void {
        this.onDocAttached();
    }

    override onDisable(): void {
        this.onDocDetached();
    }

    onDocAttached(docCtrl?: MathDocCtrl): void {
        docCtrl = docCtrl ? docCtrl : this.getFocusedMathDocCtrls()?.[0];
        if (!docCtrl) return;

        if (this.unsubscribe) this.unsubscribe();

        const subsc = docCtrl.docEditor.latexChanged$.subscribe(state => {
            this.updateContext({
                latex: state.latex,
                errors: state.errors,
            });
        });

        this.focusedDocId = docCtrl.state?.globalId || '';
        this.unsubscribe = () => subsc.unsubscribe();

        this.updateContext(docCtrl.docEditor.latexChanged$.value);
    }

    onDocDetached(docCtrl?: MathDocCtrl): void {
        if (!docCtrl || docCtrl.state.globalId === this.focusedDocId) {
            if (this.unsubscribe) {
                this.unsubscribe();
                this.unsubscribe = undefined;
            }
            this.focusedDocId = undefined;
        }
    }

    private updateContext = (state: LatexState) => {
        if (this.getFocusedMathDocCtrls().length !== 1) {
            this.changeToolState(DEFAULT_CONTEXT);
            return;
        }
        this.changeToolState({
            latex: state.latex,
            errors: state.errors,
        });
    };

    private changeToolState(newState: MathContext) {
        this.toolState = { ...newState };
        this.toolbar.update('InsertLatexTool', this.toolState);
    }
}
