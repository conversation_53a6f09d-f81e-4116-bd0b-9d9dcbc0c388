mat-slide-toggle.my-slide {
    --mdc-switch-selected-handle-color: white;
    --mdc-switch-selected-pressed-handle-color: white;
    --mdc-switch-selected-hover-handle-color: white;
    --mdc-switch-selected-focus-handle-color: white;
    --mdc-switch-selected-pressed-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-hover-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-focus-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-track-color: rgb(var(--P1));
    --mdc-switch-selected-pressed-track-color: rgb(var(--P1));
    --mdc-switch-selected-hover-track-color: rgb(var(--P1));
    --mdc-switch-selected-focus-track-color: rgb(var(--P1));
    --mdc-switch-selected-icon-color: rgb(var(--P1));
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--selected .mdc-switch__handle {
        outline: 2px solid rgb(var(--P1));
    }

    --mdc-switch-unselected-handle-color: white;
    --mdc-switch-unselected-pressed-handle-color: white;
    --mdc-switch-unselected-hover-handle-color: white;
    --mdc-switch-unselected-focus-handle-color: white;
    --mdc-switch-unselected-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-pressed-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-hover-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-focus-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-icon-color: rgb(var(--BW2));
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--unselected .mdc-switch__handle {
        outline: 2px solid rgb(var(--BW2));
    }

    --mdc-switch-disabled-selected-handle-color: white;
    --mdc-switch-disabled-selected-pressed-handle-color: white;
    --mdc-switch-disabled-selected-hover-handle-color: white;
    --mdc-switch-disabled-selected-focus-handle-color: white;
    --mdc-switch-disabled-selected-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-pressed-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-hover-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-focus-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-icon-color: rgb(var(--BW4));

    --mdc-switch-disabled-unselected-handle-color: white;
    --mdc-switch-disabled-unselected-pressed-handle-color: white;
    --mdc-switch-disabled-unselected-hover-handle-color: white;
    --mdc-switch-disabled-unselected-focus-handle-color: white;
    --mdc-switch-disabled-unselected-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-pressed-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-hover-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-focus-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-icon-color: rgb(var(--BW4));

    ::ng-deep .mdc-switch {
        --mdc-switch-disabled-handle-opacity: 1;
        --mdc-switch-disabled-selected-icon-opacity: 1;
        --mdc-switch-disabled-track-opacity: 1;
        --mdc-switch-disabled-unselected-icon-opacity: 1;
    }

    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle,
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle {
        outline: 2px solid rgb(var(--BW4));
    }
}

mat-slide-toggle.my-slide .mdc-form-field {
    display: none !important;
}

mat-slide-toggle.my-slide--mixed {
    --mdc-switch-selected-pressed-state-layer-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-hover-state-layer-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-focus-state-layer-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-track-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-pressed-track-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-hover-track-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-focus-track-color: rgb(var(--SC4)) !important;
    --mdc-switch-selected-icon-color: rgb(var(--SC4));
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--selected .mdc-switch__handle {
        outline: 2px solid rgb(var(--SC4)) !important;
    }
}

mat-slide-toggle.my-slide--unset {
    --mdc-switch-unselected-handle-color: white;
    --mdc-switch-unselected-pressed-handle-color: white;
    --mdc-switch-unselected-hover-handle-color: white;
    --mdc-switch-unselected-focus-handle-color: white;
    --mdc-switch-unselected-track-color: rgb(var(--SC3));
    --mdc-switch-unselected-pressed-track-color: rgb(var(--SC3));
    --mdc-switch-unselected-hover-track-color: rgb(var(--SC3));
    --mdc-switch-unselected-focus-track-color: rgb(var(--SC3));
    --mdc-switch-unselected-icon-color: rgb(var(--SC3));
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--unselected .mdc-switch__handle {
        outline: 2px solid rgb(var(--SC3));
    }
}
