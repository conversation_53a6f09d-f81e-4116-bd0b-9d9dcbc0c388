import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ChangeToolEventData, ToolBar, ToolEventListener, ToolState } from '@viclass/editor.core';
import {
    ElementItem,
    ElementItemActionType,
    GeometryTool,
    GeometryToolBar,
    GeometryToolType,
    GeoToolEventData,
    ListElementToolState,
} from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { getObjectTypeDisplay } from '../geo.object.type';
import { FilterPipe } from './filter.pipe';
import { ListElementItemComponent } from './list-element-item/list.element.item.component';
import { ListElementItem } from './list.element.model';

@Component({
    selector: 'tb-list-element',
    templateUrl: './list.element.tool.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, FilterPipe, ListElementItemComponent, FormsModule],
})
export class ListElementToolComponent implements OnInit, OnDestroy, AfterViewInit {
    @Output() onClose = new EventEmitter<unknown>();
    @Input() toolBar!: GeometryToolBar;

    protected elements: BehaviorSubject<Map<number, BehaviorSubject<ListElementItem>>> = new BehaviorSubject(new Map());

    protected searchText: string;

    @ViewChild('searchbar') protected searchbar: ElementRef;

    private toolListener = ListElementToolComponent.ListElementToolListener(this);

    constructor(private changeDetectorRef: ChangeDetectorRef) {}

    ngOnInit(): void {
        this.state.elements
            .map(
                it =>
                    <ListElementItem>{
                        name: it.name,
                        relIdx: it.relIdx,
                        depIdxes: it.depIdxes,
                        hidden: it.hidden,
                        highlighted: it.highlighted,
                        selected: it.selected,
                        icon: getObjectTypeDisplay(it.elType, it.relType),
                    }
            )
            .forEach(it => {
                const o = new BehaviorSubject(it);
                this.elements.value.set(it.relIdx, o);
            });

        this.elements.next(this.elements.value);
        this.toolBar.registerToolListener(this.toolListener);
    }

    ngOnDestroy(): void {
        this.toolBar.unregisterToolListener(this.toolListener);
    }

    ngAfterViewInit(): void {
        this.searchbar.nativeElement.focus();
    }

    get state(): ListElementToolState {
        return this.toolBar.toolState('ListElementTool') as ListElementToolState;
    }

    onAction(relIdx: any, actionType: ElementItemActionType) {
        const state = this.state;
        state.action = {
            actionType: actionType,
            relIdx: relIdx,
        };
        this.toolBar.update('ListElementTool', state);
    }

    private static ListElementToolListener(
        _p: ListElementToolComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: GeoToolEventData): GeoToolEventData {
                if ((eventData.toolType as GeometryToolType) == 'ListElementTool') {
                    const changeEvent = eventData as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
                    if (!changeEvent.changes) return eventData;
                    if (changeEvent.changes.has('elements')) {
                        const prevElements = changeEvent.changes.get('elements').previousValue as ElementItem[];
                        const currElements = changeEvent.changes.get('elements').currentValue as ElementItem[];

                        const deletedElements = prevElements.filter(
                            prev => !currElements.find(curr => prev.relIdx == curr.relIdx)
                        );
                        deletedElements.forEach(it => {
                            _p.elements.value.delete(it.relIdx);
                        });
                        const elements = currElements.map(
                            it =>
                                <ListElementItem>{
                                    name: it.name,
                                    relIdx: it.relIdx,
                                    depIdxes: it.depIdxes,
                                    hidden: it.hidden,
                                    highlighted: it.highlighted,
                                    selected: it.selected,
                                    icon: getObjectTypeDisplay(it.elType, it.relType),
                                }
                        );
                        for (const element of elements) {
                            let o = _p.elements.value.get(element.relIdx);
                            if (!o) {
                                o = new BehaviorSubject(element);
                                _p.elements.value.set(element.relIdx, o);
                                _p.elements.next(_p.elements.value);
                            } else {
                                o.next(element);
                            }
                        }
                    }
                    _p.changeDetectorRef.markForCheck();
                }
                return eventData;
            }
        })();
    }
}
