<div class="w-full h-[90%]">
    <div class="flex flex-row" (keydown)="$event.stopPropagation()">
        <input #searchbar type="text" tabindex="0" class="outline-none w-100 p-1" [(ngModel)]="searchText" />
        <span class="ml-auto vcon vcon-general vcon_general_search"></span>
    </div>
    <div class="bg-P1 h-[1px]" style="margin: 0.5rem 0"></div>
    <div class="w-full h-full overflow-auto">
        <div *ngFor="let el of elements.value | keyvalue | filter: searchText">
            <tb-list-element-item
                [editMode]="state.editMode"
                [element]="el"
                (onAction)="onAction(el.value.relIdx, $event)"></tb-list-element-item>
        </div>
    </div>
</div>
