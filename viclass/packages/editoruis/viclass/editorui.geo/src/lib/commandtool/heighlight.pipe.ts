import { Pipe, type PipeTransform } from '@angular/core';
import { type ConstraintTemplateData, ParamKind } from '@viclass/editor.geo';

@Pipe({ name: 'heighlight', standalone: true })
export class HeighlightPipe implements PipeTransform {
    transform(tplData: ConstraintTemplateData, ...args: any[]): string {
        if (!tplData) return '';

        const paramKindValues = Object.values(ParamKind);
        const regex = new RegExp(
            '({(' +
                paramKindValues.join('|') +
                ')})|(\\((' +
                paramKindValues.join('|') +
                ')\\))|(\\[(' +
                paramKindValues.join('|') +
                ')\\])',
            'gd'
        );

        let preResult: any;
        let curResult: any;
        let result = '';

        let inputIndex = 0;

        const tmpString = tplData.template;
        const tplModel = tplData.possibleConstraints[0];

        while ((curResult = regex.exec(tmpString)) != null) {
            const preMatchedGroup = this.findMatchedGroup(preResult);
            const curMatchedGroup = this.findMatchedGroup(curResult);

            const substr = curResult.input.substring(preMatchedGroup ? preMatchedGroup[1] : 0, curMatchedGroup?.[0]);

            // text
            if (substr) {
                result += substr;
            }

            // input
            const inputField: string = curResult.input.substring(curMatchedGroup?.[0], curMatchedGroup?.[1]);
            const replacement = tplModel.tplDescription?.[inputIndex++];
            if (replacement) {
                result += inputField.replace(inputField.substring(1, inputField.length - 1), replacement);
            } else {
                result += inputField;
            }

            preResult = curResult;
        }

        // text
        const preMatchedGroup = this.findMatchedGroup(preResult);
        if (preMatchedGroup && preMatchedGroup[1] < tmpString.length) {
            result += preResult.input.substring(preMatchedGroup[1], tmpString.length);
        }

        const str = (result + ' ')
            .replace(/\[/g, '<span>[</span>')
            .replace(/\]/g, '<span>]</span>')
            .replace(/\{/g, '<span>{</span>')
            .replace(/\}/g, '<span>}</span>')
            .replace(/\s/g, '&nbsp;');

        return str;
    }

    private findMatchedGroup(parsedResult: any): number[] | undefined {
        if (!parsedResult) return undefined;

        // match with {}
        if (parsedResult.indices[1]) return parsedResult.indices[1];

        // match with ()
        if (parsedResult.indices[3]) return parsedResult.indices[3];

        // match with []
        if (parsedResult.indices[5]) return parsedResult.indices[5];

        return undefined;
    }
}
