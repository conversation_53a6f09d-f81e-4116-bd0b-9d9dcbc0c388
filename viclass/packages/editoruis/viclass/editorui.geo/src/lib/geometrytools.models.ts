import {
    GeoCreateDocumentTool,
    GeometryToolBar,
    GeometryToolType,
    GeoObjectType,
    InputCommandTool,
    RenameElementTool,
    UpdatePropTool,
} from '@viclass/editor.geo';

export interface CommunicationEvent<T> {
    source: any;
    eventType: T;
    eventData: any;
}

export type GeoEditorControllerEvent = 'switch-tool';

export class GeometryTools {
    geoToolbar: GeometryToolBar;

    createDocumentTool: GeoCreateDocumentTool;
    updatePropTool: UpdatePropTool;
    renameElementTool: RenameElementTool;
    inputCommandTool: InputCommandTool;

    constructor(toolBar: GeometryToolBar) {
        if (!toolBar) return;

        this.geoToolbar = toolBar;

        this.createDocumentTool = toolBar.getTool('CreateDocumentTool') as GeoCreateDocumentTool;
        this.updatePropTool = toolBar.getTool('UpdatePropTool') as UpdatePropTool;
        this.renameElementTool = toolBar.getTool('RenameElementTool') as RenameElementTool;
        this.inputCommandTool = toolBar.getTool('InputCommandTool') as InputCommandTool;
    }

    activeTool(): GeometryToolType {
        return this.geoToolbar.activeTool?.toolType;
    }
}

export type ButtonData<T> = {
    name: T;
    iconClasses: string;
    onClick?: (btnName: T) => void;
    label?: string;
    children?: ButtonData<T>[];
    key?: GeoObjectType;
    viName?: string;
    // child tool when selected do not want to show on toolbar
    ignoreShowInToolbar?: boolean;
};
