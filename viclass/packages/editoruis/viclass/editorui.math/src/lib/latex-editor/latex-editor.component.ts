import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnInit,
    Output,
    SimpleChanges,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MathSyntaxError } from '@viclass/editor.math';
import { isNil } from 'lodash';

@Component({
    selector: 'lib-latex-editor',
    standalone: true,
    imports: [CommonModule, FormsModule],
    templateUrl: './latex-editor.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LatexEditorComponent implements OnInit, OnChanges {
    @Input()
    currentLatex: string = '';

    @Input()
    disabled: boolean = true;

    @Input()
    syntaxErrors: MathSyntaxError[] = [];

    latex: string = '';

    focused = false;

    @Output()
    close = new EventEmitter<void>();

    @Output()
    latexChanged = new EventEmitter<string>();

    ngOnInit(): void {
        this.latex = this.currentLatex;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (!this.focused && changes['currentLatex'] && changes['currentLatex'].currentValue !== this.latex) {
            this.latex = changes['currentLatex'].currentValue;
        }
    }

    handleLatexChange($event: Event) {
        const value = ($event.target as HTMLTextAreaElement).value;
        if (isNil(value)) return;

        this.latex = value;
        this.latexChanged.emit(this.latex);
    }

    getSyntaxErrorMessage(error: MathSyntaxError): string {
        const argPart = error.arg ? ` (tại: "${error.arg}")` : '';
        switch (error.code) {
            case 'unknown-command':
                return `Lệnh không xác định${argPart}`;
            case 'unknown-environment':
                return `Môi trường không xác định${argPart}`;
            case 'invalid-command':
                return `Lệnh không hợp lệ trong ngữ cảnh hiện tại${argPart}`;
            case 'unbalanced-braces':
                return `Thiếu hoặc dư dấu ngoặc nhọn${argPart}`;
            case 'unbalanced-environment':
                return `Môi trường không được đóng đúng${argPart}`;
            case 'unbalanced-mode-shift':
                return `Dấu chuyển chế độ ($, \\[, ...) không cân đối${argPart}`;
            case 'missing-argument':
                return `Thiếu tham số${argPart}`;
            case 'too-many-infix-commands':
                return `Quá nhiều lệnh infix trong một nhóm${argPart}`;
            case 'unexpected-command-in-string':
                return `Lệnh không hợp lệ trong chuỗi${argPart}`;
            case 'missing-unit':
                return `Thiếu đơn vị cho kích thước${argPart}`;
            case 'unexpected-delimiter':
                return `Dấu phân cách không hợp lệ${argPart}`;
            case 'unexpected-token':
                return `Ký tự không mong đợi${argPart}`;
            case 'unexpected-end-of-string':
                return `Kết thúc biểu thức đột ngột${argPart}`;
            case 'improper-alphabetic-constant':
                return `Hằng số ký tự không đúng định dạng${argPart}`;
            default:
                return `Lỗi cú pháp${argPart}`;
        }
    }
}
