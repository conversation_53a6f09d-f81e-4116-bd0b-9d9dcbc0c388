import type { EditorLookup } from '@viclass/editor.core';
import { Environment, MFEConfCreator, MFESpec, MFEDescription, MFEConfRequest } from 'src/app.model';

const create: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const edLookup: EditorLookup = {
        editorType: 'MathGraphEditor',
        lookup: {
            type: 'module',
            remoteName: 'editor.magh',
            remoteEntry: `${env.scheme}://${env.domain}/modules/editor.magh/editor.magh.js`,
            exposedModule: './editor.magh',
        },
        settings: {
            apiUri: `${env.scheme}://${env.domain}/magh`,
            operationMode: 'CLOUD',
        },
    };

    return {
        item: edLookup.editorType,
        impl: edLookup,
    };
};

export default create;
