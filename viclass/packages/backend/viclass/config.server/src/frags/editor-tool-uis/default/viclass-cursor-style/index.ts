import { Environment, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    return {
        item: 'ViclassCursorStyle',
        ui: {
            type: 'module',
            remoteName: 'viclass.theme.cursor',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/viclass.theme.cursor.js`,
            exposedModule: './viclass.theme.cursor',
        },
    };
}
