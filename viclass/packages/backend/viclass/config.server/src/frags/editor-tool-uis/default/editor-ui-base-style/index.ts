import { Environment, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    return {
        item: 'EditorUIBaseStyle',
        ui: {
            type: 'module',
            remoteName: 'editorui.theme',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/editorui.theme.js`,
            exposedModule: './editorui.theme',
        },
    };
}
