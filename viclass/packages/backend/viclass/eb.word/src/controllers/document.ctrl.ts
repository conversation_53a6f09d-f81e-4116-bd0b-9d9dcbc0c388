import {
    BadRequestException,
    Body,
    Controller,
    Get,
    InternalServerErrorException,
    Logger,
    Post,
    Query,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FetchDocResponse } from '@viclass/editor.word';
import mongoose from 'mongoose';
import * as Y from 'yjs';
import { YDocDbGatewayService } from '../db/ydoc-db.gateway.service';
import { SettingsGatewayService } from '../db/settings.gateway.service';

const log = new Logger('WordCtrl');

@Controller()
export class DocumentCtrl {
    constructor(
        private conf: ConfigService,
        private readonly yDb: YDocDbGatewayService,
        private readonly settingsService: SettingsGatewayService
    ) {}

    @Get('document/fetch')
    async fetchDocumentState(@Query('globalId') id: string): Promise<FetchDocResponse> {
        if (!mongoose.isObjectIdOrHexString(id)) throw new BadRequestException({ message: `Wrong Id ${id}` });

        const yDoc: Y.Doc = await this.yDb.getWordYDoc(id);
        const state = Y.encodeStateAsUpdateV2(yDoc);

        yDoc.destroy();

        const settings = await this.settingsService.loadSettings(id);
        return {
            id: id,
            viewportElClass: this.conf.get('viewportElClass'),
            content: Buffer.from(state).toString('base64'),
            version: 1, // should be the clock,
            settingJSON: settings?.settingsJSON,
            headingOverridesJSON: settings?.headingOverridesJSON,
        };
    }

    @Post('document/create')
    async createDoc(): Promise<FetchDocResponse> {
        log.debug('Creating new word document');

        try {
            const pojo = await this.yDb.createDocument();
            return {
                id: pojo.docName,
                viewportElClass: this.conf.get('viewportElClass'),
                content: '',
                version: pojo.clock,
                settingJSON: '',
                headingOverridesJSON: '',
            };
        } catch (error) {
            log.error(error);
            throw new InternalServerErrorException({
                message: 'Unable to create a new document',
            });
        }
    }

    @Post('documents/duplicate')
    async duplicateDocs(@Body() docGlobalIds: string[]): Promise<object> {
        try {
            const mapping = {};
            log.debug('Duplicate word document');
            for (const globalId of docGlobalIds) {
                if (mongoose.isObjectIdOrHexString(globalId)) {
                    const newDoc = await this.yDb.duplicateDocument(globalId);

                    mapping[globalId] = newDoc;
                    log.debug(`mapping ${globalId} to ${newDoc}`);
                    log.debug(`mapping ${JSON.stringify(mapping)}`);
                }
            }
            log.debug(`mapping after duplicate ${JSON.stringify(mapping)}`);
            return { mapping: mapping };
        } catch (error) {
            log.error(error);
            throw new InternalServerErrorException({
                message: 'Unable to duplicate document',
            });
        }
    }
}
