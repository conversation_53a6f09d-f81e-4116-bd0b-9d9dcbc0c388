## Docker

To build for production, first, the workspace image must be built as the image of this depends on the workspace image. Check the workspace README for more details.

```bash
# create viclass context if not yet exist
docker context create viclass --docker host=ssh://user@host

# switch docker viclass context if not yet switch
docker context use viclass
```

Build from the project folder

```
docker build . -f ./prod.DOCKERFILE -t viclass/eb.magh
```

To run for production, expose the port 8012.

```bash

docker run --add-host=host.docker.internal:host-gateway --name eb.magh -p 8012:8012 viclass/eb.magh

```
