import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { debounceTime, Subject, tap } from 'rxjs';
import { MathDocColName, MathDocMongoDocument, MathDocPojo } from './db.mongo.schema';

export const messageSync = 0;

const logger = new Logger('MathDocGatewayService');

@Injectable()
export class MathDocGatewayService {
    docSavingRegulator$: Subject<MathDocPojo> = new Subject();

    constructor(
        @InjectModel(MathDocColName)
        private mathModel: Model<MathDocMongoDocument>,
        private config: ConfigService
    ) {
        this.docSavingRegulator$
            .pipe(
                debounceTime(1000),
                tap(pojo => this.updateDocument(pojo))
            )
            .subscribe();
    }

    async loadDocument(id: string): Promise<MathDocMongoDocument> {
        return await this.mathModel.findById(id).exec();
    }

    async createDocument(): Promise<MathDocPojo> {
        const doc = await this.mathModel.create({
            version: 0,
            latex: '',
            value: '',
        });

        return doc.toObject();
    }

    async insertDocument(pojo: MathDocPojo): Promise<MathDocPojo> {
        pojo.id = undefined;
        logger.log(`Inserting document ${JSON.stringify(pojo)}`);
        const doc = await this.mathModel.create(pojo);
        return doc.toObject();
    }

    async updateDocument(pojo: MathDocPojo) {
        logger.log(`Saving document ${pojo.id}`);
        return await this.mathModel
            .findOneAndUpdate(
                { _id: pojo.id },
                {
                    version: pojo.version,
                    latex: pojo.latex,
                    value: pojo.value,
                },
                { new: true }
            )
            .exec();
    }

    async duplicateDocument(id: string): Promise<MathDocPojo> {
        const originalDoc = await this.mathModel.findById(id).lean();
        if (!originalDoc) {
            throw new NotFoundException(`Math document ID ${id} not found`);
        }

        const clonedDoc = structuredClone(originalDoc);
        clonedDoc.version = 0;
        delete clonedDoc._id;

        const result = await this.mathModel.create(clonedDoc);
        return result.toObject();
    }
}
