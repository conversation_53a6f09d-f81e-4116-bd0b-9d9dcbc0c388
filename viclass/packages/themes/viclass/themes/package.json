{"name": "@viclass/themes", "version": "0.0.1", "peerDependencies": {"bootstrap": "*"}, "dependencies": {"tslib": "^2.6.3"}, "scripts": {"build": "cd ../../../.. && npx webpack --config ./packages/themes/viclass/themes/webpack.js"}, "exports": {"./common": {"default": "./src/common.scss"}, "./button": {"default": "./src/button.scss"}, "./form": {"default": "./src/form.scss"}, "./cursor": {"default": "./src/cursor.scss"}, "./embed": {"default": "./src/embed.scss"}, "./worddoc": {"default": "./src/word.doc.default.scss"}, "./math-static": {"default": "./src/mathlive-static.scss"}, "./vi-theme": {"default": "./src/vi-theme.scss"}, "./vcon": {"default": "./assets/fonts/style.css"}}}