import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { classroomErrorHandler } from '../../error-handler';
import { ViErr } from '@viclass/editor.core';
import { ClassroomCriticalError } from '@viclass/editor.coordinator/classroom';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: 'connection-error-popup',
    templateUrl: './connection.error.popup.component.html',
})
export class ConnectionErrorPopupComponent {
    @Input() err: ViErr;

    protected get closeable(): boolean {
        return !(this.err instanceof ClassroomCriticalError);
    }

    protected reload() {
        window.location.reload();
    }

    protected close() {
        classroomErrorHandler.dismissError('CONNECTION_ERROR');
    }
}
