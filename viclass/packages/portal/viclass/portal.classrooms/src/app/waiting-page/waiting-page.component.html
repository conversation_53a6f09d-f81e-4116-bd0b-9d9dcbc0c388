<div class="min-h-full min-w-[290px] flex flex-col">
    <!-- adding gap for minimum distance between share and waiting image -->
    <div class="grow flex items-center justify-center py-[100px]">
        <!-- adding padding to ensure the waiting image still in the center -->
        <div class="flex flex-col items-center">
            <div class="flex justify-center items-center mt-[15px]">
                <strong>
                    <span class="text-[36px] min-w-[200px]" tabindex="1" *ngIf="!(editing$ | async)">{{
                        (ls.details$.get(lsId) | async).title
                    }}</span>
                    <input
                        [(ngModel)]="title"
                        id="lession-title"
                        class="text-[36px] min-w-[20px] block w-fit"
                        tabindex="1"
                        *ngIf="editing$ | async"
                        [attr.contenteditable]="editing$ | async" />
                </strong>
                <ng-template [ngIf]="isCreator | async">
                    <a
                        *ngIf="!(editing$ | async)"
                        class="vi-btn vi-btn-normal vi-btn-outline ml-[20px]"
                        (click)="switchEditTitle()">
                        <i class="vcon vcon-general vcon_edit"></i>
                    </a>
                    <button
                        *ngIf="editing$ | async"
                        class="vi-btn vi-btn-normal vi-btn-outline ml-[20px]"
                        [disabled]="(updatingTitle$ | async) || title?.trim()?.length > 50"
                        [spinner]="updatingTitle$"
                        (click)="acceptEditTitle()">
                        <i class="vcon vcon-general vcon_general_yes"></i>
                    </button>
                    <button
                        *ngIf="editing$ | async"
                        class="vi-btn vi-btn-normal vi-btn-outline ml-[20px]"
                        [disabled]="updatingTitle$ | async"
                        (click)="switchEditTitle()">
                        <i class="vcon vcon-general vcon_delete"></i>
                    </button>
                </ng-template>
            </div>
            <div class="text-red-500 text-center py-SP1" *ngIf="(editing$ | async) && title?.trim()?.length > 50">
                ! Tên buổi học tối đa gồm 50 ký tự
            </div>
            <img src="assets/img/waiting-page-banner.svg" />
            <div class="mt-[15px] flex justify-center gap-[10px]">
                <ng-template [ngIf]="sessionStatus | async" [ngSwitch]="sessionStatus | async">
                    <strong>
                        <span class="text-SC1">Buổi học </span>
                        <span *ngSwitchCase="'NOT_STARTED'" class="">đang chờ bắt đầu</span>
                        <span *ngSwitchCase="'STARTED'" class="">đang diễn ra</span>
                        <span *ngSwitchCase="'ENDED'" class="">đã kết thúc</span>
                        <span *ngSwitchCase="'CLOSED'" class="">đã bị hủy</span>
                    </strong>
                </ng-template>
                <ng-template [ngIf]="!(isCreator | async)" [ngSwitch]="regStatus | async">
                    <div *ngSwitchCase="'WAITING_CONFIRMED'" class="vi-status-tag status-tag-waiting-confirm">
                        <span class="status-tag-content">Chờ xác nhận</span>
                    </div>
                    <div *ngSwitchCase="null" class="vi-status-tag status-tag-not-registered">
                        <span class="status-tag-content">Chưa đăng ký</span>
                    </div>
                    <div *ngSwitchCase="'REGISTERED'" class="vi-status-tag status-tag-registered">
                        <span class="status-tag-content">Đã đăng ký</span>
                    </div>
                    <div *ngSwitchCase="'REJECTED'" class="vi-status-tag status-tag-rejected">
                        <span class="status-tag-content">Đã bị từ chối</span>
                    </div>
                    <div *ngSwitchCase="'CANCELLED'" class="vi-status-tag status-tag-rejected">
                        <span class="status-tag-content">Đã hủy đăng ký</span>
                    </div>
                </ng-template>
            </div>
            <div *ngIf="!(isLoaded$ | async); else loaded" class="h-[60px] flex justify-center items-center gap-3">
                <img class="object-cover h-[1.15rem] w-[1.15rem]" src="assets/img/mini-spinner.svg" />
                <p class="italic text-BW2 text-sm">Đang tải thông tin lớp học...</p>
            </div>
            <ng-template #loaded>
                <div *ngIf="registeredUsers$ | async as registeredUsers" class="mt-[10px]">
                    <div class="flex flex-col justify-center items-center gap-2">
                        <div class="flex -space-x-4 items-end">
                            <img
                                *ngFor="let user of registeredUsers | slice: 0 : 10"
                                [src]="user.avatarUrl || '/static/assets/images/avatar-man.png'"
                                alt="{{ user.email }}"
                                class="avatar-img rounded-full w-[30px] h-[30px] border border-BW7" />
                            <div *ngIf="registeredUsers.length > 10" class="more-icon">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                        <div *ngIf="ls.settings$.get(lsId) | async as settings" class="text-P1">
                            {{ registeredUsers.length }} / {{ settings.maxRegistration }}
                        </div>
                    </div>
                </div>
            </ng-template>

            <div class="flex justify-center items-center mt-[10px] gap-[20px]">
                <ng-template [ngIf]="isCreator | async" [ngIfElse]="notCreator">
                    <ng-template [ngIf]="isCreator | async" [ngSwitch]="sessionStatus | async">
                        <button
                            *ngSwitchCase="'NOT_STARTED'"
                            class="vi-btn vi-btn-normal vi-btn-focus"
                            (click)="startLSession()"
                            [disabled]="startingLSession$ | async"
                            [spinner]="startingLSession$">
                            Bắt đầu
                        </button>
                        <button
                            *ngSwitchCase="'ENDED'"
                            class="vi-btn vi-btn-normal vi-btn-focus"
                            (click)="startLSession()"
                            [disabled]="startingLSession$ | async"
                            [spinner]="startingLSession$">
                            Mở lại buổi học?
                        </button>
                        <span *ngSwitchCase="'CLOSED'" class="vi-btn vi-btn-normal vi-btn-focus">Đã bị hủy</span>
                    </ng-template>
                </ng-template>
                <ng-template #notCreator>
                    <ng-template [ngIf]="isMemberOrWaiting | async" [ngIfElse]="register">
                        <button
                            class="vi-btn vi-btn-normal vi-btn-focus"
                            (click)="unregisterLSession()"
                            [disabled]="unregisteringLSession$ | async"
                            [spinner]="unregisteringLSession$">
                            Hủy đăng ký
                        </button>
                    </ng-template>
                    <ng-template #register>
                        <ng-template [ngIf]="isFull$ | async" [ngIfElse]="notFull">
                            <span class="vi-btn vi-btn-normal vi-btn-disable">Đã hết chỗ</span>
                        </ng-template>
                        <ng-template #notFull>
                            <button
                                class="vi-btn vi-btn-normal vi-btn-focus"
                                [disabled]="(registeringLSession$ | async) || !(isLoaded$ | async)"
                                [spinner]="registeringLSession$"
                                (click)="registerLSession()">
                                Đăng ký vào lớp
                            </button>
                        </ng-template>
                    </ng-template>
                </ng-template>
                <!-- <a class="vi-btn vi-btn-normal vi-btn-outline">Chia sẻ</a> -->
                <a class="vi-btn vi-btn-normal vi-btn-outline" href="/"
                    ><i class="vcon vcon-general vcon_logo-icon"></i><span>Về trang chủ</span></a
                >
            </div>

            <div *ngIf="(isFull$ | async) && !(isCreator | async)" class="text-red-500 text-center mt-[20px]">
                <span class="vcon vcon-general vcon_general_warning">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                </span>
                Lớp học hiện tại đã đạt giới hạn số lượng người tham gia
                <span *ngIf="ls.settings$.get(lsId) | async as settings">({{ settings.maxRegistration }} người)</span
                ><br />
                Bạn có thể thử tham gia lại sau hoặc đăng ký lớp khác.
            </div>
        </div>
    </div>
    <div class="py-[20px] bg-BW5 flex md:items-center items-stretch justify-center max-md:flex-col max-md:gap-[20px]">
        <div
            class="flex md:w-[50%] items-center md:justify-end justify-center gap-[10px] pl-[20px] max-md:pr-[20px] md:pr-[25px]">
            <span><strong>Chia&nbsp;sẻ</strong></span>
            <div class="h-[18px] max-w-[250px] grow text-[12px] overflow-clip relative">
                <span class="absolute">{{ shareLink }}</span>
            </div>
            <button
                class="vi-btn vi-btn-normal vi-btn-focus"
                [disabled]="copiedShareLink$ | async"
                (click)="copyToClipboard(shareLink)">
                {{ (copiedShareLink$ | async) ? 'Đã&nbsp;sao&nbsp;chép' : 'Sao&nbsp;chép' }}
            </button>
        </div>
        <!-- Social icon -->
        <div
            class="flex md:w-[50%] items-center md:justify-start justify-center gap-[10px] md:grow pr-[20px] max-md:pl-[20px] md:pl-[25px]">
            <a class=""
                ><img
                    class="h-[38px] rounded-[12px] shadow-[0px_2px_2px_0_rgb(var(--BW1)/0.25)]"
                    src="assets/img/social-zalo.svg"
            /></a>
            <a class=""
                ><img
                    class="h-[38px] rounded-[12px] shadow-[0px_2px_2px_0_rgb(var(--BW1)/0.25)]"
                    src="assets/img/social-facebook.svg"
            /></a>
            <a class=""
                ><img
                    class="h-[38px] rounded-[12px] shadow-[0px_2px_2px_0_rgb(var(--BW1)/0.25)]"
                    src="assets/img/social-gmail.svg"
            /></a>
            <a class=""
                ><img
                    class="h-[38px] rounded-[12px] shadow-[0px_2px_2px_0_rgb(var(--BW1)/0.25)]"
                    src="assets/img/social-twitter.svg"
            /></a>
            <a class=""
                ><img
                    class="h-[38px] rounded-[12px] shadow-[0px_2px_2px_0_rgb(var(--BW1)/0.25)]"
                    src="assets/img/social-fb-messenger.svg"
            /></a>
        </div>
    </div>
</div>
