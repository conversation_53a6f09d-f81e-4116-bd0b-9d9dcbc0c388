package portal.beta.pojo

/**
 * Data class representing the information needed to send a beta invitation email.
 *
 * @property email Email address of the invited user
 * @property code Invitation code that the user can use to join the beta
 * @property applyBetaCodeUrl URL that the user can visit to apply the invitation code
 */
data class InviteJoinBeta(
    val email: String,
    val code: String,
    val applyBetaCodeUrl: String?,
)

/**
 * Data class representing the information needed to send a beta invitation email with a password.
 * This is used for new users who need both an invitation code and a password to access the beta.
 *
 * @property email Email address of the invited user
 * @property code Invitation code that the user can use to join the beta
 * @property password Password for the user's account
 * @property applyBetaCodeUrl URL that the user can visit to apply the invitation code
 */
data class InviteJoinBetaWithPassword(
    val email: String,
    val code: String,
    val password: String,
    val applyBetaCodeUrl: String?,
)
