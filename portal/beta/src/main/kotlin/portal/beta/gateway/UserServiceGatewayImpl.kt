package portal.beta.gateway

import io.grpc.ManagedChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.asCompletableFuture
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.beta.koin.NOTIFICATION_SERVICE_CHANNEL
import portal.beta.koin.USER_SERVICE_CHANNEL
import portal.beta.models.*
import proto.portal.user.UserMessage.*
import proto.portal.user.UserServiceGrpcKt
import java.util.concurrent.CompletableFuture

@Singleton
class UserServiceGatewayImpl(
    @Named(USER_SERVICE_CHANNEL) private val userChannel: ManagedChannel,
) : IUserServiceGateway {
    /**
     * Register a new user by the email-password flow
     *
     * @param ru the RegistrationUser POJO containing the registration information.
     * @return the EmailRegistrationResponse containing the result of the registration.
     */
    override suspend fun registerByEmail(ru: RegistrationUser): EmailRegistrationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val req =
            EmailRegistrationRequest.newBuilder().setUsername(ru.username).setEmail(ru.email).setPassword(ru.password)
                .setIsVerified(ru.isVerified).build()

        return stub.registerUserByEmail(req)
    }

    /**
     * Login by either username or email of the email-password flow
     *
     * @param ue the username or email to login with
     * @return a CompletableFuture containing the LoginByUsernameOrEmailResponse
     */
    override fun loginByUsernameOrEmail(ue: String): CompletableFuture<LoginByUsernameOrEmailResponse> {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = LoginByUsernameOrEmailRequest.newBuilder().setUsernameOrEmail(ue).build()

        return CoroutineScope(Dispatchers.IO).async { stub.loginByUsernameOrEmail(request) }.asCompletableFuture()
    }

    /**
     * Find user profile data by the username in the email-password flow
     *
     * @param username the username to find
     * @return a GetUserResponse containing the result of the query
     */
    override suspend fun getUserByUsername(username: String): GetUserResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserByUsernameRequest.newBuilder().setUsername(username).build()

        return stub.getUserByUsername(request)
    }

    /**
     * Find user profile data by the email
     *
     * @param email the email to find
     * @return a GetUserResponse containing the result of the query
     */
    override suspend fun getUserByEmail(email: String): GetUserResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserByEmailRequest.newBuilder().setEmail(email).build()

        return stub.getUserByEmail(request)
    }

    /**
     * Find user profile data by the list of user ids.
     * To be used by the brief-profile API
     *
     * @param userIds the list of user ids to find
     * @return a GetUserProfileByIdsResponse containing the result of the query
     */
    override suspend fun getUserProfileByIds(userIds: List<String>): GetUserProfileByIdsResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserProfileByIdsRequest.newBuilder().addAllUserId(userIds).build()

        try {
            val res = stub.getUserProfileByIds(request)
            return res
        } catch (t: Throwable) {
            throw t
        }
    }

    /**
     * Find user profile data by the username or email
     *
     * @param ue the username or email to find
     * @return a GetUserProfileResponse containing the result of the query
     */
    override suspend fun getUserProfileByUsernameOrEmail(ue: String): GetUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserByUsernameRequest.newBuilder().setUsername(ue).build()
        try {
            val res = stub.getUserProfileByUsernameOrEmail(request)
            return res
        } catch (t: Throwable) {
            throw t
        }
    }


    /**
     * Check if the registration email exists or not.
     * To be used by the registration form to block duplicate emails
     *
     * @param dto the [RegistrationEmailExistDto] containing the registration type and the email
     * @return the [CheckRegistrationEmailExistResponse] containing the result of the check
     */
    override suspend fun checkRegistrationEmailExist(dto: RegistrationEmailExistDto): CheckRegistrationEmailExistResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = CheckRegistrationEmailExistRequest.newBuilder()
            .setRegistrationType(dto.registrationType)
            .setEmail(dto.email)
            .build()

        return stub.checkRegistrationEmailExist(request)
    }

    /**
     * Find all linked registrations for a given profile email, i.e. register by both email and social accounts
     * This method is used by the profile page to display all the linked registrations.
     *
     * @param profileEmail the email of the profile to find linked registrations for
     * @return a [GetLinkedRegistrationsResponse] containing the result of the query
     */
    override suspend fun getLinkedRegistrations(profileEmail: String): GetLinkedRegistrationsResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetLinkedRegistrationsRequest.newBuilder().setProfileEmail(profileEmail)
            .build()
        return stub.getLinkedRegistrations(request)
    }

    /**
     * Sends a verification email to the user associated with the specified registration ID.
     *
     * @param registrationId the ID of the registration for which the verification email is to be sent.
     * @return a [SendVerificationEmailResponse] containing the result of the email send operation.
     */
    override suspend fun sendVerificationEmail(registrationId: String): SendVerificationEmailResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = SendVerificationEmailRequest.newBuilder()
            .setRegistrationId(registrationId)
            .build()
        return stub.sendVerificationEmail(request)
    }

    /**
     * Verifies the email associated with the given registration ID and verification code.
     *
     * @param dto the [EmailVerificationDto] containing the registration ID and verification code.
     * @return an [EmailVerificationResponse] containing the result of the email verification.
     */
    override suspend fun verifyEmail(
        dto: EmailVerificationDto, skipVerify: Boolean
    ): EmailVerificationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = EmailVerificationRequest.newBuilder().setRegistrationId(dto.registrationId)
            .setVerificationCode(dto.verificationCode).setSkipVerification(skipVerify).build()

        return stub.verifyEmail(request)
    }

    /**
     * Retrieve metadata associated with a given registration ID.
     * Include the registration ID, type (email or social), the email and is email verified.
     *
     * @param registrationId the registration ID for which to retrieve metadata.
     * @return a [RegistrationMetadataResponse] containing the retrieved metadata.
     */
    override suspend fun getRegistrationMetadata(registrationId: String): RegistrationMetadataResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = RegistrationMetadataRequest.newBuilder().setRegistrationId(registrationId).build()

        return stub.getRegistrationMetadata(request)
    }

    /**
     * We can have many registration but only one user profile of the same email.
     * Creates or merges a user profile based on the provided registration ID
     * and optional social token information when register with social account.
     *
     * @param registrationId The ID associated with the registration to create or merge a user profile.
     * @param tokenInfo Optional social token information that may include additional user data for profile creation or merging.
     * @return A [CreateOrMergeUserProfileResponse] containing the result of the operation, either creating a new user profile or merging with an existing one.
     */
    override suspend fun createOrMergeUserProfile(
        registrationId: String,
        tokenInfo: TokenInfo?,
    ): CreateOrMergeUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = CreateOrMergeUserProfileRequest.newBuilder()
            .setRegistrationId(registrationId)

        if (tokenInfo != null)
            request.setTokenInfo(tokenInfo)

        return stub.createOrMergeUserProfile(request.build())
    }

    /**
     * Verifies a social authentication token.
     * This function checks the validity of a social login token provided by the social provider.
     *
     * @param dto the [SocialLoginDto] containing the social provider and authentication token.
     * @return a [VerifySocialTokenResponse] containing the result of the token verification.
     */
    override suspend fun verifySocialToken(dto: SocialLoginDto): VerifySocialTokenResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = VerifySocialTokenRequest.newBuilder()
            .setProvider(dto.provider)
            .setAuthToken(dto.authToken)
            .build()
        return stub.verifySocialToken(request)
    }

    /**
     * Retrieve user profile data by user ID.
     *
     * @param userId The ID of the user whose profile is to be retrieved.
     * @return A [GetUserProfileResponse] containing the user profile data.
     */
    override suspend fun getUserProfileById(userId: String): GetUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserProfileByIdRequest.newBuilder().setUserId(userId).build()

        return stub.getUserProfileById(request)
    }

    /**
     * Retrieve user profile data by username.
     *
     * @param username The username of the user whose profile is to be retrieved.
     * @return A [GetUserProfileResponse] containing the user profile data.
     */
    override suspend fun getUserProfileByUsername(username: String): GetUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserProfileByUsernameRequest.newBuilder().setUsername(username).build()

        return stub.getUserProfileByUsername(request)
    }

    /**
     * Gets the email registration information for the specified username or email.
     *
     * @param usernameOrEmail The username or email of the user whose registration information is to be retrieved.
     * @return An [EmailRegInfoResponse] containing the email registration information for the specified username or email
     *         or an error status if the user is not found.
     */
    override suspend fun getEmailRegistrationInfo(usernameOrEmail: String): EmailRegInfoResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = EmailRegInfoRequest.newBuilder()
            .setUsernameOrEmail(usernameOrEmail)
            .build()
        return stub.getEmailRegInfo(request)
    }

    /**
     * Gets the user login information (with last login time and created date) for the given email.
     *
     * @param email The email of the user to get the login information for.
     * @return A [GetUserLoginInformationResponse] containing the user login information.
     */
    override suspend fun getUserLoginInformation(email: String): GetUserLoginInformationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = GetUserLoginInformationRequest.newBuilder()
            .setEmail(email).build()

        return stub.getUserLoginInformation(request)
    }

    /**
     * Updates the last login time for the user associated with the specified email.
     *
     * @param email The email of the user to update the last login time for.
     * @return An [UpdateLastLoginTimeResponse] containing the result of the update operation.
     */
    override suspend fun updateLastLoginTime(email: String): UpdateLastLoginTimeResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = UpdateLastLoginTimeRequest.newBuilder()
            .setEmail(email).build()

        return stub.updateLastLoginTime(request)
    }

    override suspend fun createOneTimeLogin(request: CreateOneTimeLoginRequest): CreateOneTimeLoginResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(userChannel)
        val request = CreateOneTimeLoginRequest.newBuilder()
            .setRegId(request.regId).build()
        return stub.createOneTimeLogin(request)
    }
}
