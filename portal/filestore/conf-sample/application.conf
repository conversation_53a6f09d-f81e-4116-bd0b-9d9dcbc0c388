ktor {
    deployment {
        port = 10000
    }
    application {
        modules = [ portal.filestore.ApplicationKt.module ]
    }
}

serverConf {
    port = 1177
}

dbConf {
    connectionString = "mongodb://devlocal.viclass.vn:27018/"
    dbName = "viclass"
    gridFsBucketName = "filestore"
}

uploadConf {
    allowFileTypes = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
    maxFileSizeBytes = 52428800
    uploadTimeoutSeconds = 1800
    fileUrlTemplate = "https://viclass.vn/filestore/download/{fileId}"
}

jwtConf {
    secret = "Bm7v6jQAsx4okmf7JvlcvoLEMbEML0fA"
    expiresInMinutes = 30
}

cacheServiceConf {
    host = "redis://localhost"
    port = 6379
    uploadedToken {
        expiration = 1800
        prefix = "_token_uploaded_:"
    }
}