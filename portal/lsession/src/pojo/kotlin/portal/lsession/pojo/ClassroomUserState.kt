package portal.lsession.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty
import portal.datastructures.lsession.RaiseHandStatus
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus

/**
 * State of the user when he/she has joined the classroom of a lsession
 */
data class ClassroomUserState @BsonCreator constructor(
    @BsonProperty("shareScreenStatus") var shareScreenStatus: ShareScreenStatus? = null,
    @BsonProperty("raiseHandStatus") val raiseHandStatus: RaiseHandStatus,
    @BsonProperty("availableStatus") var availableStatus: UserAvailableStatus,
    @BsonProperty("requestPinTabState") var requestPinTabState: List<RequestPinTabState> = emptyList(),
    @BsonProperty("joinedTime") var joinedTime: Long? = null,
)
