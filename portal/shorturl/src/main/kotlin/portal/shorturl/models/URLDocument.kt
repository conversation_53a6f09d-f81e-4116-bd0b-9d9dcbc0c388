package portal.shorturl.models

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import java.time.LocalDateTime

@Serializable
data class URLDocument(
    @BsonId
    val id: String? = null,

    @BsonProperty("original")
    val original: String,

    @BsonProperty("short")
    val short: String,

    @BsonProperty("created")
    @Contextual
    val created: LocalDateTime,

    @BsonProperty("clicks")
    val clicks: Int = 0
)
