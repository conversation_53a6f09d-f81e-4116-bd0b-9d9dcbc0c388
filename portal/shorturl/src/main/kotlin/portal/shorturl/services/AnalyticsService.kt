package portal.shorturl.services

import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Single
import portal.shorturl.dbgateway.URLGateway
import portal.shorturl.models.URLDocument
import org.koin.core.annotation.Single as Koin<PERSON>ing<PERSON>

@KoinSingle
class AnalyticsService(
    private val urlGateway: URLGateway
) : Logging {

    fun getAnalytics(): Single<AnalyticsData> {
        logger.debug("[ANALYTICS] Generating analytics data")

        return urlGateway.getAllUrls().map { urls ->
            val totalUrls = urls.size
            val totalClicks = urls.sumOf { it.clicks }
            val topUrls = urls.sortedByDescending { it.clicks }.take(10)
            val recentUrls = urls.sortedByDescending { it.created }.take(10)

            AnalyticsData(
                totalUrls = totalUrls,
                totalClicks = totalClicks,
                averageClicksPerUrl = if (totalUrls > 0) totalClicks.toDouble() / totalUrls else 0.0,
                topUrls = topUrls,
                recentUrls = recentUrls
            )
        }.doOnSuccess { analytics ->
            logger.info("[ANALYTICS] Generated analytics: ${analytics.totalUrls} URLs, ${analytics.totalClicks} total clicks")
        }.doOnError { error ->
            logger.error("[ANALYTICS] Error generating analytics", error)
        }
    }
}

data class AnalyticsData(
    val totalUrls: Int,
    val totalClicks: Int,
    val averageClicksPerUrl: Double,
    val topUrls: List<URLDocument>,
    val recentUrls: List<URLDocument>
)
