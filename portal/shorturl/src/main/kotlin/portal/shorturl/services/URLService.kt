package portal.shorturl.services

import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Maybe
import io.reactivex.rxjava3.core.Single
import portal.shorturl.configuration.Configuration
import portal.shorturl.dbgateway.URLGateway
import portal.shorturl.models.ShortenRequest
import portal.shorturl.models.ShortenResponse
import portal.shorturl.models.URLDocument
import portal.shorturl.utility.ShortCodeGenerator
import portal.shorturl.utility.URLUtils
import java.time.LocalDateTime
import org.koin.core.annotation.Single as KoinSingle

@KoinSingle
class URLService(
    private val urlGateway: URLGateway, private val config: Configuration
) : Logging {

    fun shortenUrl(
        request: ShortenRequest, clientIP: String
    ): Single<ShortenResponse> {
        logger.info("[SHORTEN] Request started from $clientIP")

        if (request.url.isBlank()) {
            logger.warn("[SHORTEN] Empty URL provided from $clientIP")
            return Single.error(IllegalArgumentException("URL is required"))
        }

        if (!URLUtils.isValidUrl(request.url)) {
            logger.warn("[SHORTEN] Invalid URL provided from $clientIP: ${request.url}")
            return Single.error(IllegalArgumentException("Invalid URL format"))
        }

        logger.info("[SHORTEN] Processing URL: ${request.url} from $clientIP")

        // Check if URL already exists
        return urlGateway.findByOriginalUrl(request.url).switchIfEmpty(
            // URL doesn't exist, create new one
            createNewShortUrl(request.url)
        ).toSingle().map { urlDoc ->
            ShortenResponse(
                short_url = "${config.baseUrl}/${urlDoc.short}", original = urlDoc.original
            )
        }.doOnSuccess { response ->
            logger.info("[SHORTEN] Successfully created/retrieved short URL: ${request.url} -> ${response.short_url}")
        }.doOnError { error ->
            logger.error("[SHORTEN] Error processing URL: ${request.url}", error)
        }
    }

    private fun createNewShortUrl(originalUrl: String): Maybe<URLDocument> {
        logger.debug("[SHORTEN] Creating new short URL for: $originalUrl")

        val shortCode = ShortCodeGenerator.generateShortCode()
        val urlDocument = URLDocument(
            original = originalUrl, short = shortCode, created = LocalDateTime.now(), clicks = 0
        )

        return urlGateway.insertUrl(urlDocument).toMaybe()
    }

    fun redirectUrl(
        shortCode: String, currentHost: String, clientIP: String, userAgent: String, referer: String
    ): Single<RedirectResult> {
        logger.info("[REDIRECT] Request for short code: $shortCode from $clientIP (User-Agent: $userAgent, Referer: $referer)")

        return urlGateway.findByShortCode(shortCode).toSingle()
            .onErrorResumeNext { Single.error(NoSuchElementException("URL not found")) }.flatMap { urlDoc ->
                logger.info("[REDIRECT] Found URL: $shortCode -> ${urlDoc.original} (current clicks: ${urlDoc.clicks})")

                // Increment click count
                urlGateway.incrementClickCount(shortCode).map { urlDoc }.doOnSuccess {
                    logger.debug("[REDIRECT] Successfully incremented click count for $shortCode")
                }.doOnError { error ->
                    logger.warn("[REDIRECT] Error updating click count for $shortCode", error)
                }.onErrorReturn { urlDoc } // Continue even if click count update fails
            }.map { urlDoc ->
                val sameDomain = URLUtils.isSameDomain(currentHost, urlDoc.original)
                logger.info("[REDIRECT] Domain check - Current: $currentHost, Target: ${urlDoc.original}, Same domain: $sameDomain")

                if (sameDomain) {
                    RedirectResult.SameDomain(urlDoc.original)
                } else {
                    RedirectResult.ExternalDomain(urlDoc.original)
                }
            }.doOnSuccess { result ->
                when (result) {
                    is RedirectResult.SameDomain -> logger.info("[REDIRECT] Redirecting to same domain URL: $shortCode -> ${result.url}")
                    is RedirectResult.ExternalDomain -> logger.info("[REDIRECT] Showing warning page for external redirect: $shortCode -> ${result.url}")
                }
            }.doOnError { error ->
                logger.error("[REDIRECT] Error processing redirect for $shortCode", error)
            }
    }

    fun getUrlStats(shortCode: String, clientIP: String): Single<URLDocument> {
        logger.info("[STATS] Request for stats of short code: $shortCode from $clientIP")

        return urlGateway.findByShortCode(shortCode).toSingle()
            .onErrorResumeNext { Single.error(NoSuchElementException("URL not found")) }.doOnSuccess { urlDoc ->
                logger.info("[STATS] Found URL stats: $shortCode -> ${urlDoc.original} (clicks: ${urlDoc.clicks}, created: ${urlDoc.created})")
            }.doOnError { error ->
                logger.error("[STATS] Error getting stats for $shortCode", error)
            }
    }

    fun getAllUrls(): Single<List<URLDocument>> {
        logger.debug("[URLS] Fetching all URLs")
        return urlGateway.getAllUrls()
    }

    fun searchUrls(query: String): Single<List<URLDocument>> {
        logger.debug("[URLS] Searching URLs with query: $query")
        return urlGateway.searchUrls(query)
    }

    fun deleteUrl(shortCode: String): Single<Boolean> {
        logger.info("[DELETE] Deleting URL with short code: $shortCode")
        return urlGateway.deleteByShortCode(shortCode).doOnSuccess { success ->
            if (success) {
                logger.info("[DELETE] Successfully deleted URL: $shortCode")
            } else {
                logger.warn("[DELETE] URL not found for deletion: $shortCode")
            }
        }
    }
}

sealed class RedirectResult {
    data class SameDomain(val url: String) : RedirectResult()
    data class ExternalDomain(val url: String) : RedirectResult()
}
