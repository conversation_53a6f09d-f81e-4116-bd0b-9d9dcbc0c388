package portal.shorturl.koin

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import portal.shorturl.configuration.Configuration
import portal.shorturl.configuration.DatabaseConfig
import portal.shorturl.configuration.ServerConfig
import java.io.File
import kotlin.system.exitProcess

@Module
class ConfigurationModule : Logging {

    @Singleton
    fun provideConfiguration(mapper: ObjectMapper): Configuration {
        val config: Configuration
        try {
            val path = "conf/config.json"
            val jsonString: String = File(path).readText(Charsets.UTF_8)
            config = mapper.readValue(jsonString, Configuration::class.java)
        } catch (t: Throwable) {
            logger.error("Exception when load configurations... ", t)
            exitProcess(1)
        }
        return config
    }

    @Singleton
    fun provideObjectMapper(): ObjectMapper {
        return jacksonObjectMapper()
    }

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig {
        return config.serverConf
    }

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig {
        return config.dbConf
    }
}
