package portal.shorturl.koin

import com.mongodb.reactivestreams.client.MongoClient
import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import org.bson.Document
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import portal.shorturl.configuration.Configuration

@Module
@ComponentScan("portal.shorturl.dbgateway")
class DatabaseModule {

    @Singleton
    fun provideMongoClient(config: Configuration): MongoClient {
        return MongoClients.create(config.dbConf.mongoUri)
    }

    @Singleton
    fun provideMongoCollection(
        client: MongoClient,
        config: Configuration
    ): MongoCollection<Document> {
        return client.getDatabase(config.dbConf.databaseName)
            .getCollection(config.dbConf.collectionName)
    }
}
