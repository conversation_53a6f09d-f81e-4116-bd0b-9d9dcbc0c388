package portal.shorturl.utility

import common.libs.logger.Logging
import kotlin.random.Random

object ShortCodeGenerator : Logging {
    private const val CHARSET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    private const val LENGTH = 6

    fun generateShortCode(): String {
        val result = StringBuilder()
        repeat(LENGTH) {
            result.append(CHARSET[Random.nextInt(CHARSET.length)])
        }
        val shortCode = result.toString()
        logger.debug("[UTILS] Generated short code: $shortCode")
        return shortCode
    }
}
