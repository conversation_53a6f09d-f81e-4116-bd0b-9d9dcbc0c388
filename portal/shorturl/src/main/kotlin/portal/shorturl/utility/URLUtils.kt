package portal.shorturl.utility

import common.libs.logger.Logging
import java.net.URL

object URLUtils : Logging {

    /**
     * Checks if the target URL is from the same domain as the current host
     */
    fun isSameDomain(currentHost: String, targetURL: String): Boolean {
        logger.debug("[UTILS] Checking domain similarity - Current: $currentHost, Target: $targetURL")

        return try {
            val parsedURL = URL(targetURL)

            // If no host in parsed URL, it's a relative URL (same domain)
            if (parsedURL.host.isEmpty()) {
                logger.debug("[UTILS] Target URL is relative, considering same domain")
                return true
            }

            // Compare hosts (remove port if present)
            val currentDomain = currentHost.split(":")[0]
            val targetDomain = parsedURL.host.split(":")[0]

            val sameDomain = currentDomain == targetDomain
            logger.debug("[UTILS] Domain comparison result - Current: $currentDomain, Target: $targetDomain, Same: $sameDomain")
            sameDomain
        } catch (e: Exception) {
            logger.error("[UTILS] Error parsing target URL $targetURL", e)
            false
        }
    }

    /**
     * Validates if a URL is properly formatted
     */
    fun isValidUrl(url: String): Boolean {
        return try {
            URL(url)
            true
        } catch (e: Exception) {
            false
        }
    }
}
