plugins {
    id "hnct.build"
    id "kotlin"
    id "io.ktor.plugin"
    id "application"
    id "com.google.devtools.ksp"
    id "kotlinx-serialization"
}

version = "1.0.0"

application {
    mainClass.set("portal.backend.ApplicationKt")
}

tasks.withType(AbstractCopyTask) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

sourceSets {
    main {
        resources {
            srcDirs += [files("conf")]
        }
    }
}

dependencies {
    internal {
        implementation ":jayeson.lib.access", "jayeson:jayeson.lib.access:$accessVs", false
        implementation(["id": ":jayeson.lib.access", "src": ["ktor"]], "jayeson:jayeson.lib.access-ktor:$accessVs", false)
        implementation(["id": ":jayeson.lib.access", "src": "deprecated"], "jayeson:jayeson.lib.access-deprecated:$accessVs", false)
        implementation ":jayeson.lib.utility", "jayeson:jayeson.lib.utility:2.1.0", false

        implementation ":jayeson.lib.session", "jayeson:jayeson.lib.session:$sessionVs", false
        implementation(['id': ":jayeson.lib.session", "src": "lettuce"], "jayeson:jayeson.lib.session-lettuce:$sessionVs", false)
        implementation(['id': ":jayeson.lib.session", "src": "memory"], "jayeson:jayeson.lib.session-memory:$sessionVs", false)

        implementation([id: ':portal.grpc', src: ['user']], "viclass:portal.grpc-user:1.0.0", true)
        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
        implementation([id: ':common.libs', src: ['captcha']], "viclass:common.libs-captcha:1.0.0", true)
        implementation([id: ':common.libs', src: ['cache']], "viclass:common.libs-cache:1.0.0", true)
        implementation([id: ':portal.datastructures', src: ['lsession']], "viclass:portal.datastructures-lsession:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['lsession']], "viclass:portal.grpc-lsession:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['classroom']], "viclass:portal.grpc-classroom:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['configurations']], "viclass:portal.grpc-configurations:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['metadataDoc']], "viclass:portal.grpc-metadataDoc:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['filestore']], "viclass:portal.grpc-filestore:1.0.0", true)
        implementation([id: ':portal.lsession', src: ['pojo']], "viclass:portal.lsession-pojo:1.0.0", true)

        implementation([id: ':portal.grpc', src: ['notification']], "viclass:portal.grpc-notification:1.0.0", true)
        implementation([id: ':portal.notification', src: ['pojo']], "viclass:portal.notification-pojo:1.0.0", true)
        implementation([id: ':portal.user', src: ['pojo']], "viclass:portal.user-pojo:1.0.0", true)
        implementation([id: ':vinet.ccs', src: ['metadata']], "viclass:vinet.css-main:1.0.0", true)

        implementation([id: ':portal.jobrunr', src: ['api']], "viclass:portal.jobrunr:1.0.0", true)
    }

    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${jacksonVs}")

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation("io.insert-koin:koin-ktor:$koinVs")
    implementation("io.insert-koin:koin-logger-slf4j:$koinVs")
    // implementation "org.koin:koin-java:2.0.1"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:$coroutineVs"
    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"
    implementation "io.grpc:grpc-okhttp:${grpcVs}"
    implementation 'junit:junit:4.13.2'

    // ktor
    implementation "io.ktor:ktor-client-content-negotiation:$ktorVs"
    implementation "io.ktor:ktor-client-cio:$ktorVs"
    implementation("io.ktor:ktor-server-sessions:$ktorVs")
    implementation("commons-codec:commons-codec:1.16.0")
    implementation "io.ktor:ktor-client-logging:$ktorVs"
    implementation("io.ktor:ktor-server-core-jvm")
    implementation("io.ktor:ktor-server-auth-jvm")
    implementation("io.ktor:ktor-server-auth-jwt-jvm")
    implementation("io.ktor:ktor-server-netty-jvm")
    implementation("io.ktor:ktor-server-content-negotiation:$ktorVs")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktorVs")
    implementation("io.ktor:ktor-server-config-yaml:$ktorVs")
    implementation("ch.qos.logback:logback-classic:$logbackVs")
    implementation("io.ktor:ktor-server-jvm:${ktorVs}")
    implementation("io.ktor:ktor-server-freemarker:$ktorVs")

    // ktor-jackson
    implementation("io.ktor:ktor-serialization-jackson:$ktorVs")

    implementation("io.ktor:ktor-server-cors:$ktorVs")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.16.0")
    testImplementation("io.ktor:ktor-server-tests-jvm")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit:$kotlinVs")

    // redis
    implementation("io.lettuce:lettuce-core:$lettuceVs")
}
