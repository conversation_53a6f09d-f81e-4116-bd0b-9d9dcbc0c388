const path = require('path')

function removeModulePrefixesFromPath(context) {
  return context.parsedUrl.pathname.replace("\/modules", "");
}

module.exports = {
  entry: path.join(__dirname, 'index.js'),
  mode: "development",

  /**
   * Dev server configuration. A single dev server that serve all contents of all projects.
   * Some projects which are consumed as dynamic remote modules has there path rewritten here, for example 
   * - editorui.freedrawing
   * - editorui.geo
   */
  devServer: {
      port: 3000,
      allowedHosts: 'all',
      static: {
        directory: path.join(__dirname, '../dist'),
      },
      historyApiFallback: {   // if some files are not found, we rewrite it if it matches certain pattern
        index: "index.html",
        rewrites: [
            { from: /^\/classrooms\/(.*)$/, to: '/classrooms/index.html' },
            { from: /^\/lsessions\/(.*)$/, to: '/lsessions/index.html' },
            { from: /^\/homepage\/(.*)$/, to: '/homepage/index.html' },
            { from: /^\/whiteboard\/(.*)$/, to: '/whiteboard/index.html' },
            { from: /^\/modules\/whiteboard\/(.*)$/, to: removeModulePrefixesFromPath },
            { from: /^\/modules\/editorui.freedrawing\/(.*)$/, to: removeModulePrefixesFromPath },
        ],
    },
  }
};
