<!DOCTYPE html>
<html lang="en-US">

<head>
    <meta charset="utf-8" />
    <title>MathLive Performance Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="../style.css" />
    <style>
        button {
            background: none;
            border: 1px solid rgba(0, 0, 0, 0.12);
            border-radius: 4px;
            color: #0066ce;
            fill: currentColor;
            position: relative;
            height: 36px;
            line-height: 36px;
            margin: 0 18px 0 0;
            min-width: 64px;
            padding: 0 16px;
            display: inline-block;
            overflow: hidden;
            will-change: box-shadow;
            transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),
                background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            vertical-align: middle;
            -webkit-user-select: none;
            user-select: none;

            font-size: 16px;
            letter-spacing: 0.08929em;
            text-transform: uppercase;
            box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.2),
                0px 2px 2px 0px rgba(0, 0, 0, 0.14),
                0px 3px 1px -2px rgba(0, 0, 0, 0.12);
        }

        button:first-child {
            margin-left: 0;
        }

        button:hover {
            background-color: rgba(0, 0, 0, 0.08);
        }

        button:active {
            background-color: white;
        }

        button.primary,
        button.active {
            color: white;
            fill: currentColor;
            background: #0066ce;
        }

        button.primary:hover {
            background-color: rgba(0, 102, 206, 0.58);
        }

        button.primary:active {
            color: #0066ce;
            fill: currentColor;
            background-color: white;
        }

        button.round {
            width: 64px;
            height: 64px;
            border-radius: 50%;
        }

        .buttonbar {
            margin-top: 1em;
        }

        #result,
        #counter {
            margin-top: 1em;
        }
    </style>
</head>

<body>
    <header>
        <h1>MathLive Performance Test</h1>
    </header>
    <main>
        <h2>Counting from 1 to 10,000</h2>
        <div class="buttonbar">
            <div><button class="primary" id="run-button">Run</button></div>
        </div>
        <div id="counter"></div>
        <div id="result"></div>

        <h2>Slider</h2>

        <div>
            <input type="range" min="1" max="10000" value="3" class="slider" id="slider" />
            <span id="slider-output"></span>
        </div>
    </main>

    <script type="module">
        import { convertLatexToMarkup } from '/dist/mathlive.mjs';

        document
            .getElementById('run-button')
            .addEventListener('click', () => {
                document.getElementById('run-button').innerHTML =
                    'Running...';
                let el = document.getElementById('counter');
                const start = performance.now();
                for (let i = 0; i <= 10000; i++) {
                    el.innerHTML = convertLatexToMarkup('n = ' + i);
                }
                const end = performance.now();
                document.getElementById('result').innerHTML =
                    '<b>' +
                    Math.floor(end - start) +
                    '</b> ms<br>' +
                    '<b>' +
                    Math.floor((10000 * 1000) / (end - start)) +
                    '</b> op/s';
                document.getElementById('run-button').innerHTML = 'Run';
            });

        document
            .getElementById('slider')
            .addEventListener('input', (el) => {
                window.requestAnimationFrame(() => {
                    document.getElementById(
                        'slider-output'
                    ).innerHTML = convertLatexToMarkup(
                        'n = ' + el.target.value
                    );
                });
            });
    </script>
</body>

</html>