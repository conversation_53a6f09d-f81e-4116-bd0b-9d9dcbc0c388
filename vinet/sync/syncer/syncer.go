// Package syncer /**
package syncer

import (
	"encoding/json"
	"fmt"
	"net"
	"strconv"
	"viclass/vinet-synchronizer/common"
	"viclass/vinet-synchronizer/coordinator"

	"github.com/pion/logging"
	"github.com/pion/webrtc/v3"
)

type Syncer struct {
	conf     *common.Config
	allRooms map[string]*SyncRoom
	allPeers map[string]*RtcConn
}

var s *Syncer

var WRTC *webrtc.API
var wrtcConfig webrtc.Configuration

// PionLogger satisfies the interface logging.LeveledLogger
// a logger is created per subsystem in Pion, so you can have custom
// behavior per subsystem (ICE, DTLS, SCTP...)
type PionLogger struct{}

// Print all messages except trace
func (c PionLogger) Trace(msg string) {
	//fmt.Printf("[PionLogger] Debug: %s\n", msg)
}
func (c PionLogger) Tracef(format string, args ...interface{}) {
	c.<PERSON>(fmt.Sprintf(format, args...))
}

func (c PionLogger) Debug(msg string) {
	//fmt.Printf("[PionLogger] Debug: %s\n", msg)
}
func (c PionLogger) Debugf(format string, args ...interface{}) {
	c.Debug(fmt.Sprintf(format, args...))
}
func (c PionLogger) Info(msg string) {
	//fmt.Printf("customLogger Info: %s\n", msg)
}
func (c PionLogger) Infof(format string, args ...interface{}) {
	c.Info(fmt.Sprintf(format, args...))
}
func (c PionLogger) Warn(msg string) {
	fmt.Printf("[PionLogger] Warn: %s\n", msg)
}
func (c PionLogger) Warnf(format string, args ...interface{}) {
	c.Warn(fmt.Sprintf(format, args...))
}
func (c PionLogger) Error(msg string) {
	fmt.Printf("[PionLogger] Error: %s\n", msg)
}
func (c PionLogger) Errorf(format string, args ...interface{}) {
	c.Error(fmt.Sprintf(format, args...))
}

// PionLoggerFactory satisfies the interface logging.LoggerFactory
// This allows us to create different loggers per subsystem. So we can
// add custom behavior
type PionLoggerFactory struct{}

func (c PionLoggerFactory) NewLogger(subsystem string) logging.LeveledLogger {
	return PionLogger{}
}

func Init(conf *common.Config) {
	s = &Syncer{
		conf:     conf,
		allRooms: make(map[string]*SyncRoom),
		allPeers: make(map[string]*RtcConn),
	}

	if len(conf.Webrtc.Ip) != 0 || len(conf.Webrtc.Port) != 0 {
		settingEngine := webrtc.SettingEngine{
			LoggerFactory: PionLoggerFactory{},
		}

		if len(conf.Webrtc.Ip) != 0 {
			settingEngine.SetNAT1To1IPs([]string{conf.Webrtc.Ip}, webrtc.ICECandidateTypeHost)
		}

		if len(conf.Webrtc.Port) != 0 {

			// settingEngine.SetNetworkTypes([]webrtc.NetworkType{
			// 	webrtc.NetworkTypeTCP4,
			// 	webrtc.NetworkTypeTCP6,
			// })

			port, err := strconv.Atoi(conf.Webrtc.Port)

			if err != nil {
				panic(err)
			}

			udpListener, err := net.ListenUDP("udp", &net.UDPAddr{
				IP:   net.IP{0, 0, 0, 0},
				Port: port,
			})

			if err != nil {
				panic(err)
			}

			// var tcpListener net.Listener
			// tcpListener, err = net.ListenTCP("tcp", &net.TCPAddr{
			// 	IP:   net.IP{0, 0, 0, 0},
			// 	Port: port,
			// })

			// if err != nil {
			// 	panic(err)
			// }

			settingEngine.SetICEUDPMux(webrtc.NewICEUDPMux(nil, udpListener))
			// settingEngine.SetICETCPMux(webrtc.NewICETCPMux(nil, tcpListener, 8))
		}

		wrtcConfig = webrtc.Configuration{}

		WRTC = webrtc.NewAPI(webrtc.WithSettingEngine(settingEngine))
	} else {
		settingEngine := webrtc.SettingEngine{
			LoggerFactory: PionLoggerFactory{},
		}

		wrtcConfig = webrtc.Configuration{
			ICEServers: []webrtc.ICEServer{
				{
					URLs: []string{"stun:stun.l.google.com:19302"},
				},
			},
		}

		WRTC = webrtc.NewAPI(webrtc.WithSettingEngine(settingEngine))
	}

}

func NewPeerConnection() (*webrtc.PeerConnection, error) {
	if WRTC == nil {
		fmt.Println("Creating connection with standard API")

		config := webrtc.Configuration{
			ICEServers: []webrtc.ICEServer{
				{
					URLs: []string{"stun:stun.l.google.com:19302"},
				},
			},
		}

		return webrtc.NewPeerConnection(config)
	} else {
		fmt.Println("Creating connection with enhanced API", wrtcConfig)
		return WRTC.NewPeerConnection(wrtcConfig)
	}
}

func Start() {
	var signal = NewSignal(s.conf)
	signal.Start()
}

func PrepareAddPeer(peer *RtcConn) { s.PrepareAddPeer(peer) }
func AddPeer(peer *RtcConn)        { s.AddPeer(peer) }
func RemovePeer(peer *RtcConn)     { s.RemovePeer(peer) }

// AddPeer need to call sequentially for each signal
func (s *Syncer) AddPeer(peer *RtcConn) {
	if s.allRooms[peer.roomId] != nil {
		s.allRooms[peer.roomId].AddPeer(peer)
	}
}

func (s *Syncer) PrepareAddPeer(peer *RtcConn) {
	if s.allRooms[peer.roomId] == nil {
		s.allRooms[peer.roomId] = NewSyncRoom(peer.roomId, s.conf)
	}
	s.allRooms[peer.roomId].PrepareAddPeer(peer)
	s.allPeers[peer.peerId] = peer
}

// RemovePeer Remove a particular peer and destroy the peer in a room.
// This typically happens when peer is disconnected or exit the room.
//
// TODO: This method also triggers scheduling of room destruction when there's no longer any peer in the room
func (s *Syncer) RemovePeer(peer *RtcConn) {
	room := s.allRooms[peer.roomId]
	if room != nil {
		fmt.Println("removing peer ", peer.peerId, " of room ", peer.roomId)
		s.allRooms[peer.roomId].RemovePeer(peer)
		delete(s.allPeers, peer.peerId)
		fmt.Println("removed peer from global", peer.peerId)
		peer.Destroy()
		// TODO: cannot use this logic, the error throw out, need to verify later, just ignore clear and destroy
		//if len(room.peers) <= 0 {
		//	room.Destroy()
		//	delete(s.allRooms, room.roomId)
		//	fmt.Println("room ", peer.roomId, " removed")
		//}
	}
}

func UpdateRoomInfo(msg *SignalMessage) {
	roomId := msg.Data["roomId"].(string)
	presentingPeerId, isPeerIdPresent := msg.Data["presentingPeerId"]
	presentingCoordStateId, isCoordStateIdPresent := msg.Data["presentingCoordStateId"]
	pinCoordStateId, isPinCoordState := msg.Data["pinCoordStateId"]
	unpinCoordStateId, isUnpinPresent := msg.Data["unpinCoordStateId"]
	createdCoordState, isCreateNewCoord := msg.Data["createdCoordState"]
	removedCoordStateId, isRemoveCoord := msg.Data["removedCoordStateId"]

	r := s.allRooms[roomId]

	msg.ToPeer = msg.FromPeer
	msg.FromPeer = s.conf.PeerInfo.PeerId

	if r == nil {
		msg.Data = map[string]interface{}{
			"status":  1,
			"message": "failed to update room",
		}
		body, _ := json.Marshal(msg)
		req, _ := coordinator.ReplyRequest(body)
		resp, err := coordinator.HttpClient.Do(req)
		fmt.Println(resp)
		fmt.Println(err)
	} else {
		if isCreateNewCoord {
			var coordState CoordState
			jsonData, _ := json.Marshal(createdCoordState)
			json.Unmarshal(jsonData, &coordState)
			r.info.CoordStateMap[coordState.Id] = coordState
		}

		if isRemoveCoord {
			delete(r.info.CoordStateMap, removedCoordStateId.(string))
		}

		if isPeerIdPresent {
			r.info.PresentingPeer = presentingPeerId.(string)
		}

		if isCoordStateIdPresent {
			r.info.PresentingCoordState = presentingCoordStateId.(string)
		}

		if isPinCoordState {
			r.info.PinnedCoordStates = append(r.info.PinnedCoordStates, pinCoordStateId.(string))
		}

		if isUnpinPresent {
			removeElement(r.info.PinnedCoordStates, unpinCoordStateId.(string))
		}

		msg.Data = map[string]interface{}{
			"status":  0,
			"message": "update room successful",
		}
		body, _ := json.Marshal(msg)
		req, _ := coordinator.ReplyRequest(body)
		resp, err := coordinator.HttpClient.Do(req)
		fmt.Println(resp)
		fmt.Println(err)
	}
}

func RoomFailed(r *SyncRoom) { s.RoomFailed(r) }

/*
*
Inform the syncer that the room has failed, and it should be removed
*
*/
func (s *Syncer) RoomFailed(r *SyncRoom) {
	_, ok := s.allRooms[r.roomId]
	if ok {
		delete(s.allRooms, r.roomId)
	}
}

func SyncToAll(peer *RtcConn, msg *webrtc.DataChannelMessage, isReliable bool) {
	s.SyncToAll(peer, msg, isReliable)
}

func (s *Syncer) SyncToAll(peer *RtcConn, msg *webrtc.DataChannelMessage, isReliable bool) {
	room := s.allRooms[peer.roomId]

	room.SyncToAll(peer, msg, isReliable)
}

func DistributeSignal(msg *SignalMessage) {
	s.DistributeSignal(msg)
}

func (s *Syncer) DistributeSignal(msg *SignalMessage) {
	if s.allPeers[msg.FromPeer] == nil {
		fmt.Println("Message sent from unknown peer! Ignore. Peer ", msg.FromPeer, " Message Data ", msg.Data)
		return
	}

	s.allPeers[msg.FromPeer].ReceiveSignal(msg)
}
