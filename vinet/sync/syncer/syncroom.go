package syncer

import (
	"encoding/json"
	"io"
	"net/http"
	"sync"
	"viclass/vinet-synchronizer/common"
	"viclass/vinet-synchronizer/coordinator"
	"viclass/vinet-synchronizer/proto"

	gp "google.golang.org/protobuf/proto"

	"fmt"
	"log"
	"os"

	"github.com/pion/webrtc/v3"
)

type SyncRoomError int

var logger = log.New(os.Stdout, "[RtcConn Logger]: ", log.LstdFlags|log.Lshortfile)

const (
	NoErr SyncRoomError = iota
	InitErrRequestRoom
	InitErrUnmarshalRoomInfo
	InitErrReadResponse
	InitErrRoomNotFound
)

type SyncRoom struct {
	roomId    string
	conf      *common.Config
	peers     map[string]*RtcConn
	dataSaver *DataSaver
	// this ready mux is used to ensure that the room is ready before making connection to the joining peer
	readyMux *sync.RWMutex
	err      SyncRoomError
	info     RoomInfo

	syncQueue []*SyncData
	syncCond  *sync.Cond
	running   bool
}

type RoomInfoResponse struct {
	Id                   string       `json:"id"`
	Owner                string       `json:"owner"`
	DefaultCoordinator   string       `json:"defaultCoordState"`
	PresentingCoordState string       `json:"presentingCoordState"`
	PresentingPeer       string       `json:"presentingPeer"`
	PinnedCoordStates    []string     `json:"pinnedCoordStates"`
	CoordStates          []CoordState `json:"coordStates"`
}

type RoomInfo struct {
	Id                   string
	Owner                string
	DefaultCoordinator   string
	PresentingCoordState string
	PresentingPeer       string
	PinnedCoordStates    []string
	CoordStateMap        map[string]CoordState
}

type CoordState struct {
	Id      string `json:"id"`
	Owner   string `json:"ownerId"`
	Room    string `json:"roomId"`
	Version int    `json:"version"`
}

type SyncData struct {
	peer     *RtcConn
	msg      *webrtc.DataChannelMessage
	reliable bool
}

func NewSyncRoom(roomId string, conf *common.Config) *SyncRoom {
	room := &SyncRoom{
		roomId:    roomId,
		conf:      conf,
		peers:     nil,
		dataSaver: nil,
		readyMux:  &sync.RWMutex{},
		syncQueue: make([]*SyncData, 0, 100), // make an empty slice
		syncCond:  sync.NewCond(&sync.Mutex{}),
		running:   false,
		err:       -1,
	}

	room.dataSaver = NewDataSaver(conf, room)

	room.readyMux.Lock() // lock first before initialization
	go room.init()

	return room
}

// initialize the room and when ready, release the ready mutex
func (r *SyncRoom) init() {

	defer func() {
		recovered := recover()

		if recovered != nil {
			initError := recovered.(map[string]interface{})

			if initError != nil {
				fmt.Println("Unable to init sync room ", r.roomId, ". Error is ", recovered)
				r.err = initError["type"].(SyncRoomError)
				RoomFailed(r)
			}
		}

		r.readyMux.Unlock() // ensure the lock is release
	}()

	fmt.Println("Initializing room " + r.roomId)

	// retrieve room information for the first time
	req, _ := coordinator.GetRoomRequest(r.roomId)

	client := &http.Client{}

	// todo: process the request
	fmt.Println("Retrieving room information")
	response, err := client.Do(req)

	if err != nil {
		panic(map[string]interface{}{"type": InitErrRequestRoom, "err": err})
	}

	if response.StatusCode == 404 {
		panic(map[string]interface{}{"type": InitErrRoomNotFound, "err": err})
	}

	line, err := io.ReadAll(response.Body)
	var infoRes RoomInfoResponse

	if err != nil {
		panic(map[string]interface{}{"type": InitErrReadResponse, "err": err})
	}

	err = json.Unmarshal([]byte(line), &infoRes)

	if err != nil {
		panic(map[string]interface{}{"type": InitErrUnmarshalRoomInfo, "err": err, "response": line})
	}

	r.info.Id = infoRes.Id
	r.info.Owner = infoRes.Owner
	r.info.DefaultCoordinator = infoRes.DefaultCoordinator
	r.info.PresentingCoordState = infoRes.PresentingCoordState
	r.info.PresentingPeer = infoRes.PresentingPeer
	r.info.PinnedCoordStates = infoRes.PinnedCoordStates
	r.info.CoordStateMap = make(map[string]CoordState)

	for _, c := range infoRes.CoordStates {
		r.info.CoordStateMap[c.Id] = c
	}

	fmt.Println("Finish retrieving information. ", "Got room info ", r.info)

	fmt.Println("Start sync loop for room ", r.roomId)

	r.running = true
	go r.syncLoop()

}

func (r *SyncRoom) Destroy() {
	fmt.Println("destroy room ", r.roomId)
	r.running = false
	r.syncCond.Signal()
}
func (r *SyncRoom) PrepareAddPeer(peer *RtcConn) {
	// doing peer connection in a separate routine so that it doesn't block the calling routine
	// the calling routine could be the polling routine that is retrieving message from the coordinator service and shouldn't be
	// blocked in any cases
	go r.performPeerConnection(peer)
}

func (r *SyncRoom) AddPeer(peer *RtcConn) {
	if r.peers == nil {
		r.peers = make(map[string]*RtcConn)
	}

	r.peers[peer.peerId] = peer
}

func (r *SyncRoom) performPeerConnection(p *RtcConn) {
	r.readyMux.RLock() // ensure that it has done the initialization

	var err error
	if r.err == -1 {
		err = p.Connect()
	} // only connect peer when the room is ready

	if err != nil {
		fmt.Println("Some error when connect to peer ", err)
	}

	r.readyMux.RUnlock()
}

func (r *SyncRoom) RemovePeer(peer *RtcConn) {
	if r.peers == nil {
		return
	} else {
		_, hasValue := r.peers[peer.peerId]
		if hasValue {
			delete(r.peers, peer.peerId)
			fmt.Println("peer ", peer.peerId, " removed from room ", peer.roomId)
		}
	}
}

func (r *SyncRoom) SyncToAll(peer *RtcConn, msg *webrtc.DataChannelMessage, isReliable bool) {
	if peer.roomId != r.roomId {
		fmt.Println("trying to sync not matching room ", peer, "vs ", r.roomId)
		return
	}

	if !peer.IsConnected {
		return
	}

	defer func() {

		err := recover()

		if err != nil {
			fmt.Println("There are some error ", err)
		}

	}()

	data := msg.Data
	index := 0
	coordIdLength := data[index]

	index += 1
	coordIdBytes := data[index : index+int(coordIdLength)]
	coordId := string(coordIdBytes)

	index += int(coordIdLength)
	metaLen := data[index]
	index += 1
	metaBytes := data[index : index+int(metaLen)]

	meta := proto.CmdMeta{}

	gp.Unmarshal(metaBytes, &meta)

	//fmt.Println("receive cmd from coord state " + coordId + " from peer " + peer.peerId + " with meta " + meta.String())

	if validateToSave(peer, &meta, coordId, r.info) {
		r.syncCond.L.Lock()
		r.syncQueue = append(r.syncQueue, &SyncData{peer: peer, msg: msg, reliable: isReliable})
		r.syncCond.L.Unlock()
		r.syncCond.Signal()
		//fmt.Println("saving from peer" + peer.peerId + " meta " + meta.String())
	}

	if validateToSyncToOthers(peer, &meta, coordId, r.info) {
		for k, v := range r.peers {
			if k == peer.peerId {
				continue
			}

			if !v.IsConnected {
				continue
			}

			//fmt.Println("syncing from peer " + peer.peerId + " to peer " + k + " meta " + meta.String())

			v.ReceiveCmd(&CmdMsg{cmd: msg, reliable: isReliable})
		}
	}
}

func validateToSyncToOthers(peer *RtcConn, meta *proto.CmdMeta, coordId string, room RoomInfo) bool {
	isPresentingCoord := coordId == room.PresentingCoordState
	isDefaultCoord := coordId == room.DefaultCoordinator
	isPinnedCoord := contains(room.PinnedCoordStates, coordId)

	// sync able if coord state is public
	if isPresentingCoord || isDefaultCoord || isPinnedCoord {
		return true
	}

	return false
}

func validateToSave(peer *RtcConn, meta *proto.CmdMeta, coordId string, room RoomInfo) bool {
	if meta.Sequence == nil || meta.Versionable == nil {
		return false
	}

	isPresentingUser := peer.peerId == room.PresentingPeer
	isPresentingCoord := coordId == room.PresentingCoordState
	isOwnedCoord := peer.userId == room.CoordStateMap[coordId].Owner

	// case 1: user can edit NONE-PRESENTING-OWNED coords
	if isOwnedCoord && !isPresentingCoord {
		return true
	}

	// case 2: user is presenting -> can edit presenting coords
	if isPresentingUser && isPresentingCoord {
		return true
	}

	return false
}

func (r *SyncRoom) syncLoop() {

	fmt.Println("Sync loop started for room ", r.roomId)

	for r.running {

		r.syncCond.L.Lock()
		if len(r.syncQueue) == 0 {
			r.syncCond.Wait()
			r.syncCond.L.Unlock()
		} else {
			defer func() {
				err := recover()

				fmt.Println("There are some error during synchronization to data saver", err)
				r.syncCond.L.Unlock()
			}()

			sd := r.syncQueue[0]
			r.syncQueue[0] = nil
			r.syncQueue = r.syncQueue[1:]
			r.syncCond.L.Unlock()

			if r.dataSaver != nil {
				r.dataSaver.DoSave(sd.peer, sd.msg)
			}

		}
	}

	fmt.Println("Exit sync loop ", r.roomId)
}
