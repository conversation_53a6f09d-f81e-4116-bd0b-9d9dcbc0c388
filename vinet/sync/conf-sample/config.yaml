peerinfo:
  # When the server is up, the service looks for the id from here. If it is not
  # set, the service will use ccs (or i.e. classroom coordinator service) to
  # register a peer id. The registered id will be persisted into this file.
  peerid: 648ad0635dc2795ac72fe8c8
ccs:
  # Url of the coordinator service
  url: http://devlocal.viclass.vn:8000
  #url: http://host.docker.internal:8000

# configuration for webrtc to use known ip and single port
# this is useful on environment where strict security is needed
# so that we don't have to expose many ports and use excessive port
# forwarding. The IP address should be the public IP address of the SERVER
# hosting this service
webrtc:
  ip: **********

  # If running in docker, this port should match the public port
  # (forwarded port on the host machine) and the destination port
  # (port that exposed in docker container). Basically the
  # -p host-port:docker-port should use the below port configuration
  # for both host-port and docker-port
  port: 50000
