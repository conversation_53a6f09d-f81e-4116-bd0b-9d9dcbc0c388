name: Size Limit

on:
  pull_request:
    branches: [main]

# Automatically cancel in-progress actions on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.event_name == 'pull_request_target' && github.head_ref || github.ref }}
  cancel-in-progress: true

jobs:
  # This basic check runs size-limit for the current branch.
  # It will fail if the branch pushes the size over the specified budget.
  size-limit-basic:
    if: ${{ github.event.pull_request.head.repo.full_name != 'withastro/starlight' }}
    name: Check build output is within performance budget (forks)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
      - run: pnpm i
      - run: 'pnpm build:examples'
      - run: pnpm size

  # This check cannot run in forks, so is only run for PRs from this repo.
  # It will run size-limit for both `main` and the PR branch and comment in the PR with changes.
  size-limit:
    if: ${{ github.event.pull_request.head.repo.full_name == 'withastro/starlight' }}
    name: Check build output is within performance budget
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
      - name: Run size-limit
        uses: andresz1/size-limit-action@v1.8.0
        with:
          github_token: ${{ secrets.FREDKBOT_GITHUB_TOKEN }}
          build_script: 'build:examples'
          package_manager: pnpm
