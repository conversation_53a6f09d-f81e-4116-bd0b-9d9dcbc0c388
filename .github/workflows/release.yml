name: Release

on:
  push:
    branches:
      - main

jobs:
  release:
    name: Release
    if: ${{ github.repository_owner == 'withastro' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          # This makes Actions fetch all Git history so that Changesets can generate changelogs with the correct commits
          fetch-depth: 0

      - name: Setup PNPM
        uses: pnpm/action-setup@v3

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Install Dependencies
        run: pnpm i

      - name: Create Release Pull Request
        uses: changesets/action@v1
        with:
          version: pnpm run version
          publish: pnpm changeset publish
          commit: '[ci] release'
          title: '[ci] release'
        env:
          GITHUB_TOKEN: ${{ secrets.FREDKBOT_GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
